/**
 * Content Diversification Engine
 * Ensures content variation and prevents repetitive messaging
 */

export interface ContentVariationContext {
  businessType: string;
  location: string;
  platform: string;
  previousThemes?: string[];
  sessionId?: string;
}

export interface DiversificationStrategy {
  themeRotation: string[];
  messagingAngles: string[];
  ctaVariations: string[];
  creativeFocus: string[];
  tonalVariations: string[];
}

export class ContentDiversificationEngine {
  private static contentHistory: Map<string, string[]> = new Map();
  private static sessionThemes: Map<string, Set<string>> = new Map();

  /**
   * Get diversified content strategy to prevent repetition
   */
  static getDiversificationStrategy(context: ContentVariationContext): DiversificationStrategy {
    const businessKey = `${context.businessType}-${context.location}`;
    const sessionId = context.sessionId || 'default';
    
    // Track used themes for this session
    if (!this.sessionThemes.has(sessionId)) {
      this.sessionThemes.set(sessionId, new Set());
    }
    const usedThemes = this.sessionThemes.get(sessionId)!;

    // Get available themes (excluding recently used ones)
    const allThemes = this.getAllContentThemes();
    const availableThemes = allThemes.filter(theme => !usedThemes.has(theme));
    
    // If all themes used, reset and start over
    if (availableThemes.length === 0) {
      usedThemes.clear();
      availableThemes.push(...allThemes);
    }

    // Select random theme from available ones
    const selectedTheme = this.selectRandomItem(availableThemes);
    usedThemes.add(selectedTheme);

    // Get business-specific diversification
    const businessStrategy = this.getBusinessSpecificStrategy(context.businessType);
    
    return {
      themeRotation: [selectedTheme, ...this.getAlternativeThemes(selectedTheme)],
      messagingAngles: this.getRotatedMessagingAngles(businessKey),
      ctaVariations: this.getRotatedCTAs(context.businessType),
      creativeFocus: this.getCreativeFocusRotation(),
      tonalVariations: businessStrategy.tonalVariations
    };
  }

  /**
   * Get all available content themes
   */
  private static getAllContentThemes(): string[] {
    return [
      'Problem-Solution',
      'Benefit-Focused', 
      'Social-Proof',
      'Urgency-Driven',
      'Educational-Value',
      'Emotional-Connection',
      'Innovation-Showcase',
      'Community-Focused',
      'Success-Stories',
      'Behind-the-Scenes',
      'Seasonal-Relevance',
      'Local-Pride'
    ];
  }

  /**
   * Get alternative themes that complement the selected theme
   */
  private static getAlternativeThemes(selectedTheme: string): string[] {
    const themeGroups = {
      'Problem-Solution': ['Educational-Value', 'Innovation-Showcase'],
      'Benefit-Focused': ['Success-Stories', 'Social-Proof'],
      'Social-Proof': ['Community-Focused', 'Success-Stories'],
      'Urgency-Driven': ['Seasonal-Relevance', 'Innovation-Showcase'],
      'Educational-Value': ['Behind-the-Scenes', 'Innovation-Showcase'],
      'Emotional-Connection': ['Community-Focused', 'Local-Pride'],
      'Innovation-Showcase': ['Educational-Value', 'Behind-the-Scenes'],
      'Community-Focused': ['Local-Pride', 'Social-Proof'],
      'Success-Stories': ['Social-Proof', 'Emotional-Connection'],
      'Behind-the-Scenes': ['Innovation-Showcase', 'Educational-Value'],
      'Seasonal-Relevance': ['Local-Pride', 'Community-Focused'],
      'Local-Pride': ['Community-Focused', 'Emotional-Connection']
    };

    return themeGroups[selectedTheme] || ['Benefit-Focused', 'Social-Proof'];
  }

  /**
   * Get rotated messaging angles to prevent repetition
   */
  private static getRotatedMessagingAngles(businessKey: string): string[] {
    const allAngles = [
      'Expert Authority',
      'Customer Success',
      'Innovation Leader',
      'Community Partner',
      'Problem Solver',
      'Value Provider',
      'Trust Builder',
      'Growth Enabler',
      'Quality Assurance',
      'Local Champion'
    ];

    // Get previously used angles
    const usedAngles = this.contentHistory.get(`${businessKey}-angles`) || [];
    
    // Filter out recently used angles (last 3)
    const recentlyUsed = usedAngles.slice(-3);
    const availableAngles = allAngles.filter(angle => !recentlyUsed.includes(angle));
    
    // Select 3 random angles
    const selectedAngles = this.selectRandomItems(availableAngles, 3);
    
    // Update history
    this.contentHistory.set(`${businessKey}-angles`, [...usedAngles, ...selectedAngles].slice(-6));
    
    return selectedAngles;
  }

  /**
   * Get rotated CTAs based on business type
   */
  private static getRotatedCTAs(businessType: string): string[] {
    const ctasByType = {
      restaurant: [
        'Order Now', 'Book Table', 'View Menu', 'Call Now', 'Visit Today',
        'Try Our Special', 'Reserve Seat', 'Taste Excellence', 'Join Us'
      ],
      'professional services': [
        'Get Quote', 'Schedule Consultation', 'Contact Expert', 'Learn More',
        'Start Today', 'Book Appointment', 'Get Started', 'Speak to Specialist'
      ],
      retail: [
        'Shop Now', 'Browse Collection', 'Get Yours', 'Limited Offer',
        'Buy Today', 'Explore Range', 'Grab Deal', 'Visit Store'
      ],
      healthcare: [
        'Book Appointment', 'Schedule Checkup', 'Contact Clinic', 'Get Care',
        'Call Doctor', 'Visit Today', 'Health First', 'Book Now'
      ],
      fitness: [
        'Start Training', 'Join Gym', 'Book Session', 'Get Fit',
        'Try Class', 'Transform Today', 'Join Community', 'Start Journey'
      ],
      beauty: [
        'Book Treatment', 'Get Pampered', 'Transform Look', 'Visit Salon',
        'Try Service', 'Look Amazing', 'Book Session', 'Glow Today'
      ],
      technology: [
        'Get Solution', 'Start Free Trial', 'Contact Sales', 'Learn More',
        'Upgrade Now', 'Get Demo', 'Try Today', 'Innovate Now'
      ]
    };

    const businessCTAs = ctasByType[businessType] || ctasByType['professional services'];
    return this.selectRandomItems(businessCTAs, 3);
  }

  /**
   * Get creative focus rotation
   */
  private static getCreativeFocusRotation(): string[] {
    const creativeFoci = [
      'People-Centered',
      'Product-Focused', 
      'Service-Highlight',
      'Lifestyle-Oriented',
      'Process-Showcase',
      'Results-Driven',
      'Community-Building',
      'Innovation-Display'
    ];

    return this.selectRandomItems(creativeFoci, 2);
  }

  /**
   * Get business-specific strategy
   */
  private static getBusinessSpecificStrategy(businessType: string): { tonalVariations: string[] } {
    const strategies = {
      restaurant: {
        tonalVariations: ['Warm & Inviting', 'Exciting & Appetizing', 'Cozy & Familiar', 'Premium & Sophisticated']
      },
      'professional services': {
        tonalVariations: ['Professional & Trustworthy', 'Expert & Authoritative', 'Approachable & Helpful', 'Innovative & Forward-thinking']
      },
      retail: {
        tonalVariations: ['Trendy & Exciting', 'Value-Focused', 'Quality-Assured', 'Customer-Centric']
      },
      healthcare: {
        tonalVariations: ['Caring & Compassionate', 'Professional & Reliable', 'Advanced & Innovative', 'Community-Focused']
      },
      fitness: {
        tonalVariations: ['Motivational & Energetic', 'Supportive & Encouraging', 'Results-Oriented', 'Community-Driven']
      },
      beauty: {
        tonalVariations: ['Luxurious & Pampering', 'Transformative & Confident', 'Natural & Authentic', 'Trendy & Stylish']
      },
      technology: {
        tonalVariations: ['Innovative & Cutting-edge', 'Reliable & Secure', 'User-Friendly', 'Future-Forward']
      }
    };

    return strategies[businessType] || strategies['professional services'];
  }

  /**
   * Select random item from array
   */
  private static selectRandomItem<T>(items: T[]): T {
    return items[Math.floor(Math.random() * items.length)];
  }

  /**
   * Select multiple random items from array
   */
  private static selectRandomItems<T>(items: T[], count: number): T[] {
    const shuffled = [...items].sort(() => 0.5 - Math.random());
    return shuffled.slice(0, Math.min(count, items.length));
  }

  /**
   * Clear session themes (call when starting new session)
   */
  static clearSessionThemes(sessionId: string): void {
    this.sessionThemes.delete(sessionId);
  }

  /**
   * Get content variation instructions for AI prompts
   */
  static getVariationInstructions(strategy: DiversificationStrategy): string {
    return `
CONTENT VARIATION REQUIREMENTS:
- Primary Theme: ${strategy.themeRotation[0]}
- Messaging Angle: ${strategy.messagingAngles[0]}
- Creative Focus: ${strategy.creativeFocus[0]}
- Tone: ${strategy.tonalVariations[0]}
- CTA Style: ${strategy.ctaVariations[0]}

VARIATION MANDATE:
- Create UNIQUE content that differs significantly from typical ${strategy.themeRotation[0]} approaches
- Use FRESH language and avoid generic marketing phrases
- Focus on ${strategy.creativeFocus[0]} perspective
- Adopt ${strategy.tonalVariations[0]} tone throughout
- Ensure messaging reflects ${strategy.messagingAngles[0]} positioning

AVOID REPETITION:
- Do not use standard templates or formulaic language
- Create original headlines that haven't been used before
- Develop unique value propositions
- Use varied sentence structures and vocabulary
- Ensure each element (headline, subheadline, CTA) feels fresh and distinctive
    `.trim();
  }
}
