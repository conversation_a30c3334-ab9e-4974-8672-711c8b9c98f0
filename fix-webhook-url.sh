#!/bin/bash

echo "🔧 Stripe Webhook Configuration Fix"
echo "================================="
echo ""

echo "❌ WRONG URLs (what you currently have):"
echo "   https://crevo.app/api/stripe/webhook"
echo ""

echo "✅ CORRECT URLs (what you need to set):"
echo "   Production: https://crevo.app/api/webhooks/stripe"
echo "   Development: Use Stripe CLI (see below)"
echo ""

echo "📋 Steps to Fix:"
echo ""
echo "1. Go to your Stripe Dashboard:"
echo "   https://dashboard.stripe.com/webhooks"
echo ""
echo "2. Find your existing webhooks and UPDATE the endpoint URLs from:"
echo "   https://crevo.app/api/stripe/webhook"
echo "   TO:"
echo "   https://crevo.app/api/webhooks/stripe"
echo ""
echo "3. For LOCAL TESTING, use Stripe CLI:"
echo "   stripe listen --forward-to localhost:3001/api/webhooks/stripe"
echo ""
echo "4. Events to listen for:"
echo "   - checkout.session.completed"
echo "   - payment_intent.payment_failed"
echo ""
echo "5. Test the webhook endpoint:"
echo "   curl https://crevo.app/api/webhooks/stripe"
echo "   Should return: {\"status\":\"active\"}"
echo ""

echo "🔑 Webhook Secrets:"
echo "   Production: whsec_pud3vY1pfsT97COt1qGNasP4O8yMIRBR"
echo "   Test: whsec_wZMQxthtESMsB5gjicfyjMOlSlSVE860"
echo ""

echo "⚠️  CRITICAL: Do NOT listen to 'payment_intent.succeeded' for checkout"
echo "   sessions as it will create duplicate transactions!"