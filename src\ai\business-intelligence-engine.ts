/**
 * Business Intelligence Engine for Revo 1.5
 * Provides deep business context and strategic insights for content generation
 */

import type { BrandProfile } from '@/lib/types';
import type { ScheduledService } from '@/services/calendar-service';

export interface BusinessIntelligence {
  industryInsights: {
    keyTrends: string[];
    customerBehaviors: string[];
    competitiveAdvantages: string[];
    seasonalFactors: string[];
  };
  contentStrategy: {
    primaryMessaging: string[];
    emotionalHooks: string[];
    valuePropositions: string[];
    differentiators: string[];
  };
  audienceProfile: {
    demographics: string[];
    psychographics: string[];
    painPoints: string[];
    motivations: string[];
  };
  marketingAngles: {
    problemSolution: string[];
    benefitFocused: string[];
    socialProof: string[];
    urgencyCreators: string[];
  };
}

// Industry-specific intelligence database
const INDUSTRY_INTELLIGENCE = {
  restaurant: {
    keyTrends: ['farm-to-table', 'delivery optimization', 'health-conscious dining', 'local sourcing'],
    customerBehaviors: ['reviews-driven decisions', 'social media influence', 'convenience priority', 'experience seeking'],
    competitiveAdvantages: ['unique recipes', 'atmosphere', 'service quality', 'value pricing'],
    seasonalFactors: ['holiday menus', 'summer outdoor dining', 'comfort food seasons', 'fresh ingredient availability'],
    primaryMessaging: ['authentic flavors', 'fresh ingredients', 'memorable experiences', 'community gathering'],
    emotionalHooks: ['comfort', 'celebration', 'nostalgia', 'social connection'],
    valuePropositions: ['taste satisfaction', 'convenient location', 'fair pricing', 'consistent quality'],
    differentiators: ['family recipes', 'local ingredients', 'unique atmosphere', 'personal service'],
    demographics: ['local families', 'working professionals', 'food enthusiasts', 'social groups'],
    psychographics: ['convenience seekers', 'experience collectors', 'quality conscious', 'socially active'],
    painPoints: ['limited time', 'dietary restrictions', 'inconsistent quality', 'poor service'],
    motivations: ['hunger satisfaction', 'social bonding', 'convenience', 'treat experiences'],
    problemSolution: ['solve hunger quickly', 'provide gathering space', 'offer dietary options', 'ensure consistent quality'],
    benefitFocused: ['delicious meals', 'convenient location', 'friendly service', 'fair prices'],
    socialProof: ['customer favorites', 'local reputation', 'repeat customers', 'community support'],
    urgencyCreators: ['limited time offers', 'daily specials', 'seasonal items', 'popular dishes']
  },
  'professional services': {
    keyTrends: ['digital transformation', 'remote consultations', 'specialized expertise', 'outcome-based pricing'],
    customerBehaviors: ['research-heavy decisions', 'referral-based selection', 'long-term relationships', 'ROI focused'],
    competitiveAdvantages: ['specialized expertise', 'proven track record', 'personal attention', 'industry knowledge'],
    seasonalFactors: ['year-end planning', 'tax seasons', 'budget cycles', 'compliance deadlines'],
    primaryMessaging: ['expert guidance', 'proven results', 'trusted advisor', 'strategic partnership'],
    emotionalHooks: ['confidence', 'peace of mind', 'success', 'professional growth'],
    valuePropositions: ['expert knowledge', 'time savings', 'risk reduction', 'better outcomes'],
    differentiators: ['industry specialization', 'proven methodology', 'personal attention', 'long-term partnership'],
    demographics: ['business owners', 'executives', 'professionals', 'growing companies'],
    psychographics: ['success oriented', 'risk averse', 'efficiency focused', 'quality conscious'],
    painPoints: ['complex challenges', 'time constraints', 'knowledge gaps', 'compliance concerns'],
    motivations: ['business growth', 'risk mitigation', 'efficiency gains', 'competitive advantage'],
    problemSolution: ['solve complex problems', 'reduce business risks', 'save valuable time', 'ensure compliance'],
    benefitFocused: ['expert solutions', 'proven results', 'peace of mind', 'strategic advantage'],
    socialProof: ['client success stories', 'industry recognition', 'years of experience', 'satisfied clients'],
    urgencyCreators: ['limited availability', 'deadline pressures', 'market opportunities', 'regulatory changes']
  },
  retail: {
    keyTrends: ['omnichannel shopping', 'sustainable products', 'personalized experiences', 'local sourcing'],
    customerBehaviors: ['price comparison', 'online research', 'social influence', 'convenience priority'],
    competitiveAdvantages: ['product selection', 'competitive pricing', 'customer service', 'convenient location'],
    seasonalFactors: ['holiday shopping', 'back-to-school', 'seasonal fashion', 'gift-giving occasions'],
    primaryMessaging: ['quality products', 'great value', 'wide selection', 'customer satisfaction'],
    emotionalHooks: ['smart shopping', 'style confidence', 'value satisfaction', 'discovery joy'],
    valuePropositions: ['quality assurance', 'competitive prices', 'convenient shopping', 'expert advice'],
    differentiators: ['unique products', 'local focus', 'personal service', 'community connection'],
    demographics: ['local shoppers', 'value seekers', 'style conscious', 'convenience focused'],
    psychographics: ['quality conscious', 'price sensitive', 'trend aware', 'community minded'],
    painPoints: ['limited selection', 'high prices', 'poor quality', 'inconvenient shopping'],
    motivations: ['value seeking', 'style expression', 'convenience', 'quality assurance'],
    problemSolution: ['find quality products', 'get fair prices', 'save shopping time', 'ensure satisfaction'],
    benefitFocused: ['quality guarantee', 'competitive pricing', 'wide selection', 'expert guidance'],
    socialProof: ['customer reviews', 'popular items', 'local reputation', 'satisfied shoppers'],
    urgencyCreators: ['limited stock', 'seasonal items', 'special offers', 'trending products']
  },
  healthcare: {
    keyTrends: ['preventive care', 'telemedicine', 'personalized treatment', 'wellness focus'],
    customerBehaviors: ['research-driven', 'referral-based', 'insurance-conscious', 'outcome-focused'],
    competitiveAdvantages: ['medical expertise', 'modern facilities', 'patient care', 'accessibility'],
    seasonalFactors: ['flu seasons', 'allergy periods', 'wellness campaigns', 'insurance renewals'],
    primaryMessaging: ['expert care', 'patient focus', 'health outcomes', 'compassionate service'],
    emotionalHooks: ['health security', 'peace of mind', 'caring support', 'life improvement'],
    valuePropositions: ['expert diagnosis', 'effective treatment', 'preventive care', 'accessible service'],
    differentiators: ['specialized expertise', 'modern technology', 'patient-centered care', 'convenient access'],
    demographics: ['health-conscious individuals', 'families', 'seniors', 'chronic condition patients'],
    psychographics: ['health focused', 'quality seeking', 'trust oriented', 'outcome driven'],
    painPoints: ['health concerns', 'wait times', 'insurance issues', 'treatment uncertainty'],
    motivations: ['health improvement', 'pain relief', 'preventive care', 'peace of mind'],
    problemSolution: ['address health concerns', 'provide expert care', 'ensure accessibility', 'deliver results'],
    benefitFocused: ['expert treatment', 'improved health', 'peace of mind', 'quality care'],
    socialProof: ['patient testimonials', 'medical credentials', 'successful treatments', 'community trust'],
    urgencyCreators: ['health concerns', 'preventive care', 'early detection', 'treatment timing']
  },
  fitness: {
    keyTrends: ['functional fitness', 'group training', 'wellness integration', 'technology tracking'],
    customerBehaviors: ['goal-oriented', 'community-seeking', 'progress-tracking', 'motivation-needing'],
    competitiveAdvantages: ['expert trainers', 'community support', 'proven programs', 'modern equipment'],
    seasonalFactors: ['new year resolutions', 'summer prep', 'back-to-school', 'holiday recovery'],
    primaryMessaging: ['transformation results', 'expert guidance', 'supportive community', 'proven programs'],
    emotionalHooks: ['confidence building', 'energy boost', 'achievement pride', 'community belonging'],
    valuePropositions: ['fitness results', 'expert coaching', 'motivational support', 'convenient access'],
    differentiators: ['personalized programs', 'community atmosphere', 'expert trainers', 'proven results'],
    demographics: ['health enthusiasts', 'busy professionals', 'fitness beginners', 'goal-oriented individuals'],
    psychographics: ['health conscious', 'goal driven', 'community oriented', 'improvement focused'],
    painPoints: ['lack of motivation', 'time constraints', 'intimidation', 'plateau results'],
    motivations: ['health improvement', 'confidence building', 'stress relief', 'social connection'],
    problemSolution: ['overcome fitness barriers', 'provide expert guidance', 'build motivation', 'ensure results'],
    benefitFocused: ['fitness transformation', 'expert coaching', 'community support', 'proven results'],
    socialProof: ['member transformations', 'trainer expertise', 'community testimonials', 'success stories'],
    urgencyCreators: ['fitness goals', 'seasonal prep', 'health concerns', 'limited spots']
  },
  beauty: {
    keyTrends: ['natural products', 'personalized treatments', 'wellness integration', 'sustainable practices'],
    customerBehaviors: ['image-conscious', 'trend-following', 'quality-seeking', 'experience-valuing'],
    competitiveAdvantages: ['expert stylists', 'latest techniques', 'quality products', 'personalized service'],
    seasonalFactors: ['wedding seasons', 'holiday events', 'summer prep', 'new year refresh'],
    primaryMessaging: ['expert transformation', 'personalized care', 'latest trends', 'confidence building'],
    emotionalHooks: ['confidence boost', 'self-care', 'transformation joy', 'beauty enhancement'],
    valuePropositions: ['expert styling', 'personalized service', 'quality results', 'relaxing experience'],
    differentiators: ['skilled professionals', 'premium products', 'personalized approach', 'relaxing atmosphere'],
    demographics: ['style-conscious individuals', 'professionals', 'special event clients', 'self-care focused'],
    psychographics: ['appearance conscious', 'quality focused', 'trend aware', 'self-care oriented'],
    painPoints: ['appearance concerns', 'time constraints', 'poor results', 'high costs'],
    motivations: ['confidence building', 'self-expression', 'special occasions', 'self-care'],
    problemSolution: ['enhance appearance', 'provide expert care', 'ensure satisfaction', 'create confidence'],
    benefitFocused: ['stunning results', 'expert care', 'confidence boost', 'relaxing experience'],
    socialProof: ['client transformations', 'stylist expertise', 'satisfied customers', 'before/after results'],
    urgencyCreators: ['special events', 'seasonal trends', 'appointment availability', 'limited offers']
  }
};

export class BusinessIntelligenceEngine {
  /**
   * Generate comprehensive business intelligence for content strategy
   */
  static generateIntelligence(
    brandProfile: BrandProfile,
    scheduledServices?: ScheduledService[],
    realTimeContext?: any
  ): BusinessIntelligence {
    const businessType = brandProfile.businessType.toLowerCase();
    const baseIntelligence = this.getBaseIntelligence(businessType);
    
    // Enhance with brand-specific context
    const enhancedIntelligence = this.enhanceWithBrandContext(baseIntelligence, brandProfile);
    
    // Add scheduled services context
    if (scheduledServices && scheduledServices.length > 0) {
      this.addScheduledServicesContext(enhancedIntelligence, scheduledServices);
    }
    
    // Add real-time context
    if (realTimeContext) {
      this.addRealTimeContext(enhancedIntelligence, realTimeContext);
    }
    
    return enhancedIntelligence;
  }

  /**
   * Get base intelligence for business type
   */
  private static getBaseIntelligence(businessType: string): BusinessIntelligence {
    // Find matching industry intelligence
    for (const [key, intelligence] of Object.entries(INDUSTRY_INTELLIGENCE)) {
      if (businessType.includes(key) || key.includes(businessType)) {
        return {
          industryInsights: {
            keyTrends: intelligence.keyTrends,
            customerBehaviors: intelligence.customerBehaviors,
            competitiveAdvantages: intelligence.competitiveAdvantages,
            seasonalFactors: intelligence.seasonalFactors
          },
          contentStrategy: {
            primaryMessaging: intelligence.primaryMessaging,
            emotionalHooks: intelligence.emotionalHooks,
            valuePropositions: intelligence.valuePropositions,
            differentiators: intelligence.differentiators
          },
          audienceProfile: {
            demographics: intelligence.demographics,
            psychographics: intelligence.psychographics,
            painPoints: intelligence.painPoints,
            motivations: intelligence.motivations
          },
          marketingAngles: {
            problemSolution: intelligence.problemSolution,
            benefitFocused: intelligence.benefitFocused,
            socialProof: intelligence.socialProof,
            urgencyCreators: intelligence.urgencyCreators
          }
        };
      }
    }
    
    // Default professional services intelligence
    const defaultIntel = INDUSTRY_INTELLIGENCE['professional services'];
    return {
      industryInsights: {
        keyTrends: defaultIntel.keyTrends,
        customerBehaviors: defaultIntel.customerBehaviors,
        competitiveAdvantages: defaultIntel.competitiveAdvantages,
        seasonalFactors: defaultIntel.seasonalFactors
      },
      contentStrategy: {
        primaryMessaging: defaultIntel.primaryMessaging,
        emotionalHooks: defaultIntel.emotionalHooks,
        valuePropositions: defaultIntel.valuePropositions,
        differentiators: defaultIntel.differentiators
      },
      audienceProfile: {
        demographics: defaultIntel.demographics,
        psychographics: defaultIntel.psychographics,
        painPoints: defaultIntel.painPoints,
        motivations: defaultIntel.motivations
      },
      marketingAngles: {
        problemSolution: defaultIntel.problemSolution,
        benefitFocused: defaultIntel.benefitFocused,
        socialProof: defaultIntel.socialProof,
        urgencyCreators: defaultIntel.urgencyCreators
      }
    };
  }

  /**
   * Enhance intelligence with brand-specific context
   */
  private static enhanceWithBrandContext(
    intelligence: BusinessIntelligence,
    brandProfile: BrandProfile
  ): BusinessIntelligence {
    // Add location-specific insights
    if (brandProfile.location) {
      intelligence.contentStrategy.primaryMessaging.push(`local ${brandProfile.location} expertise`);
      intelligence.contentStrategy.differentiators.push(`${brandProfile.location} community focus`);
    }
    
    // Add service-specific insights
    if (brandProfile.services) {
      const services = typeof brandProfile.services === 'string' 
        ? brandProfile.services.split('\n').filter(s => s.trim())
        : Array.isArray(brandProfile.services) 
          ? brandProfile.services.map(s => typeof s === 'string' ? s : s.name || '')
          : [];
      
      services.forEach(service => {
        intelligence.contentStrategy.valuePropositions.push(`specialized ${service.toLowerCase()}`);
      });
    }
    
    // Add target audience insights
    if (brandProfile.targetAudience) {
      intelligence.audienceProfile.demographics.push(brandProfile.targetAudience);
    }
    
    return intelligence;
  }

  /**
   * Add scheduled services context
   */
  private static addScheduledServicesContext(
    intelligence: BusinessIntelligence,
    scheduledServices: ScheduledService[]
  ): void {
    const todayServices = scheduledServices.filter(s => s.isToday);
    const upcomingServices = scheduledServices.filter(s => s.isUpcoming);
    
    if (todayServices.length > 0) {
      intelligence.marketingAngles.urgencyCreators.push('today\'s featured services');
      intelligence.contentStrategy.primaryMessaging.push('today\'s special focus');
    }
    
    if (upcomingServices.length > 0) {
      intelligence.marketingAngles.urgencyCreators.push('upcoming service availability');
    }
  }

  /**
   * Add real-time context
   */
  private static addRealTimeContext(
    intelligence: BusinessIntelligence,
    realTimeContext: any
  ): void {
    if (realTimeContext.weather) {
      intelligence.contentStrategy.primaryMessaging.push(`weather-relevant messaging`);
      intelligence.marketingAngles.urgencyCreators.push('weather-driven needs');
    }
    
    if (realTimeContext.events && realTimeContext.events.length > 0) {
      intelligence.contentStrategy.primaryMessaging.push('local event connections');
      intelligence.marketingAngles.urgencyCreators.push('event-related opportunities');
    }
    
    if (realTimeContext.trends && realTimeContext.trends.length > 0) {
      intelligence.industryInsights.keyTrends.push(...realTimeContext.trends.slice(0, 2));
    }
  }

  /**
   * Get specific marketing angle for content type
   */
  static getMarketingAngle(
    intelligence: BusinessIntelligence,
    contentType: 'problem-solution' | 'benefit-focused' | 'social-proof' | 'urgency'
  ): string[] {
    switch (contentType) {
      case 'problem-solution':
        return intelligence.marketingAngles.problemSolution;
      case 'benefit-focused':
        return intelligence.marketingAngles.benefitFocused;
      case 'social-proof':
        return intelligence.marketingAngles.socialProof;
      case 'urgency':
        return intelligence.marketingAngles.urgencyCreators;
      default:
        return intelligence.marketingAngles.benefitFocused;
    }
  }

  /**
   * Get contextual insights summary for prompt building
   */
  static getInsightsSummary(intelligence: BusinessIntelligence): string {
    return `BUSINESS INTELLIGENCE:
- Key Industry Trends: ${intelligence.industryInsights.keyTrends.slice(0, 3).join(', ')}
- Customer Behaviors: ${intelligence.industryInsights.customerBehaviors.slice(0, 3).join(', ')}
- Competitive Advantages: ${intelligence.industryInsights.competitiveAdvantages.slice(0, 3).join(', ')}
- Primary Messaging: ${intelligence.contentStrategy.primaryMessaging.slice(0, 3).join(', ')}
- Emotional Hooks: ${intelligence.contentStrategy.emotionalHooks.slice(0, 3).join(', ')}
- Target Demographics: ${intelligence.audienceProfile.demographics.slice(0, 3).join(', ')}
- Key Pain Points: ${intelligence.audienceProfile.painPoints.slice(0, 3).join(', ')}
- Primary Motivations: ${intelligence.audienceProfile.motivations.slice(0, 3).join(', ')}`;
  }
}
