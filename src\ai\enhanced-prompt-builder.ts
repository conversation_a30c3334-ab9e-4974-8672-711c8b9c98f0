/**
 * Enhanced Prompt Builder for Revo 1.5
 * Replaces simple string replacement with intelligent, context-aware prompt construction
 * using proven marketing frameworks and business-specific strategies
 */

import type { BrandProfile, Platform } from '@/lib/types';
import type { ScheduledService } from '@/services/calendar-service';

// Marketing frameworks for content generation
export const MARKETING_FRAMEWORKS = {
  AIDA: {
    name: 'AIDA (Attention, Interest, Desire, Action)',
    structure: {
      attention: 'Hook the audience with compelling headline',
      interest: 'Build interest with relevant benefits',
      desire: 'Create desire through value proposition',
      action: 'Drive action with clear CTA'
    }
  },
  PAS: {
    name: 'PAS (Problem, Agitate, Solution)',
    structure: {
      problem: 'Identify customer pain point',
      agitate: 'Amplify the problem impact',
      solution: 'Present your service as the solution'
    }
  },
  BEFORE_AFTER_BRIDGE: {
    name: 'Before-After-Bridge',
    structure: {
      before: 'Current problematic situation',
      after: 'Desired future state',
      bridge: 'Your service as the path forward'
    }
  }
};

// Business-type specific content strategies
export const BUSINESS_STRATEGIES = {
  restaurant: {
    framework: 'AIDA',
    focusAreas: ['taste', 'experience', 'atmosphere', 'convenience'],
    painPoints: ['hunger', 'time constraints', 'quality concerns', 'value for money'],
    emotionalTriggers: ['satisfaction', 'comfort', 'social connection', 'indulgence'],
    ctaPatterns: ['Order Now', 'Book Table', 'Try Today', 'Reserve Now'],
    contentThemes: ['fresh ingredients', 'authentic flavors', 'family recipes', 'local favorites']
  },
  'professional services': {
    framework: 'PAS',
    focusAreas: ['expertise', 'results', 'trust', 'efficiency'],
    painPoints: ['complexity', 'time waste', 'poor results', 'unreliable providers'],
    emotionalTriggers: ['confidence', 'peace of mind', 'success', 'professionalism'],
    ctaPatterns: ['Get Quote', 'Schedule Call', 'Learn More', 'Contact Us'],
    contentThemes: ['proven expertise', 'client success', 'industry knowledge', 'reliable service']
  },
  retail: {
    framework: 'BEFORE_AFTER_BRIDGE',
    focusAreas: ['quality', 'value', 'selection', 'convenience'],
    painPoints: ['poor quality', 'high prices', 'limited options', 'inconvenient shopping'],
    emotionalTriggers: ['satisfaction', 'smart shopping', 'style', 'value'],
    ctaPatterns: ['Shop Now', 'Browse Store', 'View Products', 'Buy Today'],
    contentThemes: ['quality products', 'great prices', 'wide selection', 'customer satisfaction']
  },
  healthcare: {
    framework: 'PAS',
    focusAreas: ['care quality', 'expertise', 'accessibility', 'results'],
    painPoints: ['health concerns', 'wait times', 'impersonal care', 'uncertainty'],
    emotionalTriggers: ['health', 'peace of mind', 'trust', 'care'],
    ctaPatterns: ['Book Now', 'Schedule Visit', 'Call Today', 'Get Care'],
    contentThemes: ['expert care', 'patient focus', 'modern facilities', 'health outcomes']
  },
  fitness: {
    framework: 'BEFORE_AFTER_BRIDGE',
    focusAreas: ['results', 'motivation', 'community', 'expertise'],
    painPoints: ['lack of results', 'motivation issues', 'intimidation', 'time constraints'],
    emotionalTriggers: ['transformation', 'confidence', 'energy', 'achievement'],
    ctaPatterns: ['Start Today', 'Join Now', 'Try Free', 'Book Session'],
    contentThemes: ['transformation', 'community support', 'expert guidance', 'proven results']
  },
  beauty: {
    framework: 'BEFORE_AFTER_BRIDGE',
    focusAreas: ['transformation', 'expertise', 'self-care', 'confidence'],
    painPoints: ['appearance concerns', 'lack of time', 'poor results', 'high costs'],
    emotionalTriggers: ['confidence', 'beauty', 'self-care', 'transformation'],
    ctaPatterns: ['Book Now', 'Transform Today', 'Schedule Visit', 'Try Now'],
    contentThemes: ['expert stylists', 'latest techniques', 'personalized care', 'stunning results']
  },
  technology: {
    framework: 'PAS',
    focusAreas: ['innovation', 'efficiency', 'reliability', 'support'],
    painPoints: ['outdated systems', 'complexity', 'downtime', 'poor support'],
    emotionalTriggers: ['efficiency', 'innovation', 'reliability', 'progress'],
    ctaPatterns: ['Learn More', 'Get Started', 'Try Demo', 'Contact Us'],
    contentThemes: ['cutting-edge solutions', 'reliable systems', 'expert support', 'business growth']
  }
};

// Cultural context for authentic local content
export const CULTURAL_CONTEXTS = {
  kenya: {
    values: ['Ubuntu', 'community', 'family', 'respect'],
    communicationStyle: 'warm, relationship-focused, community-oriented',
    localElements: ['Swahili phrases', 'community references', 'family values'],
    businessApproach: 'relationship-first, community-focused, trust-building'
  },
  nigeria: {
    values: ['community', 'success', 'resilience', 'celebration'],
    communicationStyle: 'vibrant, energetic, success-oriented',
    localElements: ['Pidgin expressions', 'success stories', 'community pride'],
    businessApproach: 'achievement-focused, community-proud, dynamic'
  },
  'south africa': {
    values: ['Ubuntu', 'diversity', 'unity', 'progress'],
    communicationStyle: 'inclusive, progressive, unity-focused',
    localElements: ['multilingual greetings', 'rainbow nation', 'unity themes'],
    businessApproach: 'inclusive, progressive, community-building'
  },
  india: {
    values: ['family', 'respect', 'tradition', 'progress'],
    communicationStyle: 'respectful, family-oriented, value-conscious',
    localElements: ['Namaste', 'family references', 'festival connections'],
    businessApproach: 'family-focused, value-oriented, respectful'
  },
  usa: {
    values: ['innovation', 'opportunity', 'diversity', 'achievement'],
    communicationStyle: 'direct, opportunity-focused, achievement-oriented',
    localElements: ['regional references', 'opportunity language', 'success themes'],
    businessApproach: 'results-focused, opportunity-driven, innovative'
  },
  canada: {
    values: ['inclusivity', 'nature', 'community', 'quality'],
    communicationStyle: 'friendly, inclusive, quality-focused',
    localElements: ['friendly expressions', 'nature references', 'community values'],
    businessApproach: 'quality-focused, community-oriented, inclusive'
  }
};

// Platform-specific optimization
export const PLATFORM_OPTIMIZATIONS = {
  instagram: {
    contentLength: { min: 150, max: 300, optimal: 200 },
    hashtagCount: 5,
    tone: 'visual, inspirational, lifestyle-focused',
    contentStyle: 'story-driven, emoji-rich, community-focused',
    ctaStyle: 'action-oriented, visually appealing'
  },
  facebook: {
    contentLength: { min: 200, max: 400, optimal: 250 },
    hashtagCount: 3,
    tone: 'conversational, community-building, discussion-driving',
    contentStyle: 'longer-form, discussion-starting, inclusive',
    ctaStyle: 'conversation-starting, community-building'
  },
  linkedin: {
    contentLength: { min: 100, max: 250, optimal: 150 },
    hashtagCount: 3,
    tone: 'professional, insight-driven, industry-focused',
    contentStyle: 'expertise-showcasing, professional, value-adding',
    ctaStyle: 'professional, networking-focused'
  },
  twitter: {
    contentLength: { min: 50, max: 150, optimal: 100 },
    hashtagCount: 3,
    tone: 'concise, witty, trending-aware',
    contentStyle: 'brief, impactful, conversation-starting',
    ctaStyle: 'quick, action-oriented'
  }
};

export interface PromptBuilderContext {
  brandProfile: BrandProfile;
  platform: Platform;
  businessStrategy?: any;
  culturalContext?: any;
  platformOptimization?: any;
  scheduledServices?: ScheduledService[];
  realTimeContext?: any;
  useLocalLanguage?: boolean;
}

export class EnhancedPromptBuilder {
  /**
   * Build a comprehensive, context-aware prompt for content generation
   */
  static buildContentGenerationPrompt(context: PromptBuilderContext): string {
    const {
      brandProfile,
      platform,
      scheduledServices,
      realTimeContext,
      useLocalLanguage = false
    } = context;

    // Get business strategy
    const businessType = brandProfile.businessType.toLowerCase();
    const businessStrategy = this.getBusinessStrategy(businessType);
    
    // Get cultural context
    const culturalContext = this.getCulturalContext(brandProfile.location || '');
    
    // Get platform optimization
    const platformOpt = PLATFORM_OPTIMIZATIONS[platform.toLowerCase() as keyof typeof PLATFORM_OPTIMIZATIONS] || PLATFORM_OPTIMIZATIONS.instagram;

    // Build the comprehensive prompt
    return this.constructPrompt({
      brandProfile,
      platform,
      businessStrategy,
      culturalContext,
      platformOptimization: platformOpt,
      scheduledServices,
      realTimeContext,
      useLocalLanguage
    });
  }

  /**
   * Get business-specific strategy
   */
  private static getBusinessStrategy(businessType: string): any {
    // Find matching strategy
    for (const [key, strategy] of Object.entries(BUSINESS_STRATEGIES)) {
      if (businessType.includes(key) || key.includes(businessType)) {
        return strategy;
      }
    }
    
    // Default to professional services strategy
    return BUSINESS_STRATEGIES['professional services'];
  }

  /**
   * Get cultural context for location
   */
  private static getCulturalContext(location: string): any {
    const locationLower = location.toLowerCase();
    
    for (const [key, context] of Object.entries(CULTURAL_CONTEXTS)) {
      if (locationLower.includes(key)) {
        return context;
      }
    }
    
    // Default context
    return {
      values: ['quality', 'service', 'trust', 'results'],
      communicationStyle: 'professional, friendly, results-focused',
      localElements: [],
      businessApproach: 'quality-focused, customer-centric, professional'
    };
  }

  /**
   * Construct the final prompt using all context
   */
  private static constructPrompt(context: PromptBuilderContext & {
    businessStrategy: any;
    culturalContext: any;
    platformOptimization: any;
  }): string {
    const {
      brandProfile,
      platform,
      businessStrategy,
      culturalContext,
      platformOptimization,
      scheduledServices,
      realTimeContext,
      useLocalLanguage
    } = context;

    // Build system prompt with marketing expertise
    const systemPrompt = this.buildSystemPrompt(businessStrategy, culturalContext, platformOptimization);
    
    // Build context section
    const contextSection = this.buildContextSection(brandProfile, scheduledServices, realTimeContext);
    
    // Build framework guidance
    const frameworkGuidance = this.buildFrameworkGuidance(businessStrategy, culturalContext);
    
    // Build output specifications
    const outputSpecs = this.buildOutputSpecifications(platformOptimization, useLocalLanguage);

    return `${systemPrompt}

${contextSection}

${frameworkGuidance}

${outputSpecs}`;
  }

  /**
   * Build system prompt with marketing expertise
   */
  private static buildSystemPrompt(businessStrategy: any, culturalContext: any, platformOpt: any): string {
    return `You are an elite marketing strategist and copywriter with 15+ years of experience creating viral, conversion-focused content for ${businessStrategy.framework} campaigns.

Your expertise includes:
- ${businessStrategy.framework} framework mastery for maximum conversion
- Deep understanding of ${culturalContext.communicationStyle} communication
- ${platformOpt.tone} content optimization
- Cultural intelligence and authentic local messaging
- Psychology-driven copy that connects emotionally and drives action

Your content consistently achieves:
- 40%+ higher engagement than industry average
- Authentic, human-like tone that never sounds AI-generated
- Business-specific messaging that showcases unique value
- Cultural relevance that resonates with local audiences
- Clear, compelling calls-to-action that drive conversions`;
  }

  /**
   * Build comprehensive context section
   */
  private static buildContextSection(brandProfile: BrandProfile, scheduledServices?: ScheduledService[], realTimeContext?: any): string {
    let context = `BUSINESS CONTEXT:
- Company: ${brandProfile.businessName}
- Industry: ${brandProfile.businessType}
- Location: ${brandProfile.location}
- Target Audience: ${brandProfile.targetAudience || 'Local community'}
- Services: ${brandProfile.services || 'Professional services'}`;

    if (scheduledServices && scheduledServices.length > 0) {
      const todayServices = scheduledServices.filter(s => s.isToday);
      const upcomingServices = scheduledServices.filter(s => s.isUpcoming);
      
      if (todayServices.length > 0) {
        context += `\n- TODAY'S FEATURED SERVICES: ${todayServices.map(s => s.serviceName).join(', ')}`;
      }
      
      if (upcomingServices.length > 0) {
        context += `\n- UPCOMING SERVICES: ${upcomingServices.map(s => s.serviceName).join(', ')}`;
      }
    }

    if (realTimeContext) {
      if (realTimeContext.weather) {
        context += `\n- WEATHER CONTEXT: ${realTimeContext.weather.condition} - ${realTimeContext.weather.business_impact}`;
      }
      
      if (realTimeContext.events && realTimeContext.events.length > 0) {
        context += `\n- LOCAL EVENTS: ${realTimeContext.events.slice(0, 2).join(', ')}`;
      }
    }

    return context;
  }

  /**
   * Build framework-specific guidance
   */
  private static buildFrameworkGuidance(businessStrategy: any, culturalContext: any): string {
    const framework = MARKETING_FRAMEWORKS[businessStrategy.framework as keyof typeof MARKETING_FRAMEWORKS];
    
    return `CONTENT STRATEGY (${framework.name}):
${Object.entries(framework.structure).map(([key, value]) => `- ${key.toUpperCase()}: ${value}`).join('\n')}

BUSINESS-SPECIFIC FOCUS:
- Primary Focus Areas: ${businessStrategy.focusAreas.join(', ')}
- Customer Pain Points: ${businessStrategy.painPoints.join(', ')}
- Emotional Triggers: ${businessStrategy.emotionalTriggers.join(', ')}
- Content Themes: ${businessStrategy.contentThemes.join(', ')}

CULTURAL APPROACH:
- Communication Style: ${culturalContext.communicationStyle}
- Core Values: ${culturalContext.values.join(', ')}
- Business Approach: ${culturalContext.businessApproach}`;
  }

  /**
   * Build output specifications
   */
  private static buildOutputSpecifications(platformOpt: any, useLocalLanguage: boolean): string {
    return `OUTPUT REQUIREMENTS:

1. HEADLINE (4-6 words): Attention-grabbing, benefit-focused
   - Must include specific business value or outcome
   - Avoid generic words like "solutions", "services", "excellence"
   - Use power words that create urgency or desire

2. SUBHEADLINE (8-14 words): Expand on headline with specific benefits
   - Include concrete details, numbers, or proof points when possible
   - Connect emotionally with target audience pain points
   - Support the headline's promise with credible details

3. CAPTION (${platformOpt.contentLength.min}-${platformOpt.contentLength.max} words, optimal: ${platformOpt.contentLength.optimal}):
   - Follow ${MARKETING_FRAMEWORKS[Object.keys(MARKETING_FRAMEWORKS)[0] as keyof typeof MARKETING_FRAMEWORKS].name} structure
   - Include specific business details and unique value propositions
   - Use conversational, human tone - never sound robotic or AI-generated
   - Incorporate cultural elements naturally if appropriate
   ${useLocalLanguage ? '- Include subtle local language elements when natural and authentic' : ''}

4. CALL-TO-ACTION (2-4 words): Simple, natural, action-oriented
   - Use common, conversational language people actually use
   - Avoid business names or awkward prepositions
   - Match the business type and customer journey stage

5. HASHTAGS (${platformOpt.hashtagCount} hashtags):
   - Mix of business-specific, location-based, and trending tags
   - Avoid overly generic hashtags
   - Include industry-specific and local community hashtags

CRITICAL REQUIREMENTS:
- Sound authentically human, never AI-generated
- Be specific to this exact business and location
- Avoid repetitive patterns or generic marketing speak
- Include concrete details that prove business expertise
- Create emotional connection while maintaining professionalism

Format as JSON:
{
  "headline": "Your compelling headline here",
  "subheadline": "Your supporting subheadline here", 
  "caption": "Your engaging caption here",
  "callToAction": "Your strong CTA here",
  "hashtags": [${Array(platformOpt.hashtagCount).fill('"#SpecificHashtag"').join(', ')}]
}`;
  }
}
