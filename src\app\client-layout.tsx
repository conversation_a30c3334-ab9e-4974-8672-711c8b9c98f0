"use client";

import { Toaster } from "@/components/ui/toaster"
import { SidebarProvider } from '@/components/ui/sidebar';
import { AppSidebar } from '@/components/layout/app-sidebar';
import { AuthWrapper } from '@/components/auth/auth-wrapper-supabase';
import { UnifiedBrandProvider } from '@/contexts/unified-brand-context';
import { BrandColorProvider } from '@/components/layout/brand-color-provider';
import { DesignColorProvider } from '@/contexts/design-color-context';
import React, { Suspense, useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

// Enable the client-side AppRoute overlay for faster perceived routing.
// The AppRoute client component is lazy-loaded and will render client pages inside the layout.
const AppRouteClient = React.lazy(() => import('@/components/app-route/AppRoute').then(m => ({ default: m.default })));



function ConditionalLayout({ children, useAppRoute }: { children: React.ReactNode; useAppRoute?: boolean }) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return <div>Loading...</div>;
  }

  // Pages that should NOT show the sidebar (public pages only)
  // Hide the sidebar for any route under /auth or /billing so auth and billing pages render standalone
  const shouldHideSidebar = pathname === '/' ||
    pathname === '/features' ||
    pathname === '/pricing' ||
    pathname === '/about' ||
    pathname === '/privacy' ||
    pathname === '/terms' ||
    pathname === '/success' ||
    pathname === '/forgot-password' ||
    pathname === '/verify-password' ||
    pathname === '/change-password' ||
    (pathname ?? '').startsWith('/auth') ||
    (pathname ?? '').startsWith('/billing') ||
    pathname === '/cancel';

  if (shouldHideSidebar) {
    // For public/auth pages we keep the plain children rendering.
    return <div className="w-full">{children}</div>;
  }

  // Render the client AppRoute (lazy) instead of server-rendered children to make
  // route transitions feel instantaneous. This mounts a client overlay that lazy-loads
  // page components without a full server navigation.
  return (
    <SidebarProvider>
      <AppSidebar />
      <BrandColorProvider>
        <DesignColorProvider>
          <Suspense fallback={<div className="p-6">Loading...</div>}>
            <AppRouteClient />
          </Suspense>
        </DesignColorProvider>
      </BrandColorProvider>
    </SidebarProvider>
  );
}

export function ClientLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <>
      <AuthWrapper>
        <UnifiedBrandProvider>
          <ConditionalLayout useAppRoute={true}>
            {children}
          </ConditionalLayout>
        </UnifiedBrandProvider>
      </AuthWrapper>
      <Toaster />
    </>
  );
}