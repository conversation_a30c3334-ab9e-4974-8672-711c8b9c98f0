/**
 * Content Quality Validator for Revo 1.5
 * Validates content authenticity, business specificity, and human-like quality
 */

import type { BrandProfile } from '@/lib/types';

export interface ContentQualityScore {
  overall: number;
  authenticity: number;
  businessSpecificity: number;
  humanLikeness: number;
  culturalRelevance: number;
  actionability: number;
  issues: string[];
  recommendations: string[];
  shouldRegenerate: boolean;
}

export interface GeneratedContent {
  headline: string;
  subheadline: string;
  caption: string;
  callToAction: string;
  hashtags: string[];
}

// Generic phrases that indicate low-quality, AI-generated content
const GENERIC_PHRASES = [
  'solutions', 'excellence', 'transform', 'elevate', 'optimize', 'streamline',
  'cutting-edge', 'state-of-the-art', 'world-class', 'industry-leading',
  'comprehensive', 'innovative', 'revolutionary', 'game-changing',
  'next-level', 'take your business to the next level', 'unlock potential',
  'seamless', 'effortless', 'hassle-free', 'one-stop-shop',
  'your trusted partner', 'we understand', 'we believe',
  'journey', 'experience the difference', 'discover the power',
  'unleash', 'maximize', 'leverage', 'synergize'
];

// AI-sounding patterns that should be avoided
const AI_PATTERNS = [
  /\b(solutions?|excellence|transform|elevate)\b/gi,
  /\b(cutting-edge|state-of-the-art|world-class)\b/gi,
  /\b(comprehensive|innovative|revolutionary)\b/gi,
  /\b(seamless|effortless|hassle-free)\b/gi,
  /\b(journey|experience the difference)\b/gi,
  /\b(unlock|unleash|maximize|leverage)\b/gi,
  /\b(we understand|we believe|we're here)\b/gi,
  /\b(take your .+ to the next level)\b/gi,
  /\b(discover the power of)\b/gi,
  // NEW: Specific problematic phrases from user feedback
  /\bProduce Financial Technology Breakthrough\b/gi,
  /\bFinancial Technology Breakthrough\b/gi,
  /\bTechnology Breakthrough\b/gi,
  /\bBreakthrough (GH|KE|NG|ZA)\b/gi,
  /\b(your trusted partner in)\b/gi
];

// Business-specific indicators that show authentic content
const BUSINESS_SPECIFICITY_INDICATORS = {
  restaurant: ['menu', 'ingredients', 'recipe', 'flavor', 'dish', 'cuisine', 'chef', 'dining', 'taste'],
  'professional services': ['consultation', 'expertise', 'analysis', 'strategy', 'planning', 'advisory', 'guidance'],
  retail: ['products', 'inventory', 'selection', 'brands', 'merchandise', 'shopping', 'store'],
  healthcare: ['treatment', 'diagnosis', 'patient', 'medical', 'health', 'care', 'wellness', 'therapy'],
  fitness: ['workout', 'training', 'exercise', 'fitness', 'strength', 'cardio', 'muscle', 'gym'],
  beauty: ['styling', 'treatment', 'hair', 'skin', 'makeup', 'beauty', 'salon', 'spa'],
  technology: ['software', 'system', 'data', 'digital', 'platform', 'application', 'tech']
};

// Human conversation patterns that indicate authentic content
const HUMAN_PATTERNS = [
  /\b(actually|really|pretty|quite|very|super|totally)\b/gi,
  /\b(you know|you see|here's the thing|honestly|frankly)\b/gi,
  /\b(let me tell you|I'll be honest|the truth is)\b/gi,
  /\b(what I love about|what makes us different)\b/gi,
  /\b(we've been|we started|we focus on)\b/gi
];

// Cultural authenticity indicators
const CULTURAL_INDICATORS = {
  kenya: ['community', 'ubuntu', 'harambee', 'family', 'together', 'karibu', 'jambo'],
  nigeria: ['community', 'family', 'success', 'together', 'celebration', 'achievement'],
  'south africa': ['ubuntu', 'community', 'unity', 'together', 'diversity', 'rainbow'],
  india: ['family', 'tradition', 'respect', 'community', 'namaste', 'together'],
  usa: ['opportunity', 'success', 'achievement', 'innovation', 'community', 'local'],
  canada: ['community', 'friendly', 'quality', 'local', 'together', 'inclusive']
};

export class ContentQualityValidator {
  /**
   * Validate content quality and authenticity
   */
  static validateContent(
    content: GeneratedContent,
    brandProfile: BrandProfile,
    useLocalLanguage: boolean = false
  ): ContentQualityScore {
    const scores = {
      authenticity: this.scoreAuthenticity(content),
      businessSpecificity: this.scoreBusinessSpecificity(content, brandProfile.businessType),
      humanLikeness: this.scoreHumanLikeness(content),
      culturalRelevance: this.scoreCulturalRelevance(content, brandProfile.location || '', useLocalLanguage),
      actionability: this.scoreActionability(content)
    };

    const overall = Object.values(scores).reduce((sum, score) => sum + score, 0) / Object.keys(scores).length;

    const issues: string[] = [];
    const recommendations: string[] = [];

    // Identify issues and recommendations
    if (scores.authenticity < 70) {
      issues.push('Contains generic marketing language');
      recommendations.push('Use more specific, business-focused language');
    }

    if (scores.businessSpecificity < 60) {
      issues.push('Lacks business-specific details');
      recommendations.push('Include specific services, products, or industry terms');
    }

    if (scores.humanLikeness < 65) {
      issues.push('Sounds AI-generated or robotic');
      recommendations.push('Use more conversational, natural language');
    }

    if (scores.culturalRelevance < 50 && useLocalLanguage) {
      issues.push('Missing cultural context');
      recommendations.push('Include location-specific or cultural elements');
    }

    if (scores.actionability < 70) {
      issues.push('Weak or unclear call-to-action');
      recommendations.push('Use stronger, more specific action words');
    }

    return {
      overall: Math.round(overall),
      authenticity: Math.round(scores.authenticity),
      businessSpecificity: Math.round(scores.businessSpecificity),
      humanLikeness: Math.round(scores.humanLikeness),
      culturalRelevance: Math.round(scores.culturalRelevance),
      actionability: Math.round(scores.actionability),
      issues,
      recommendations,
      shouldRegenerate: overall < 65 || scores.authenticity < 60
    };
  }

  /**
   * Score content authenticity (avoid generic marketing speak)
   */
  private static scoreAuthenticity(content: GeneratedContent): number {
    let score = 100;
    const allText = `${content.headline} ${content.subheadline} ${content.caption} ${content.callToAction}`.toLowerCase();

    // Check for generic phrases
    GENERIC_PHRASES.forEach(phrase => {
      if (allText.includes(phrase.toLowerCase())) {
        score -= 15; // Heavy penalty for generic phrases
      }
    });

    // Check for AI patterns
    AI_PATTERNS.forEach(pattern => {
      const matches = allText.match(pattern);
      if (matches) {
        score -= matches.length * 10; // Penalty per match
      }
    });

    // Bonus for specific details (numbers, names, concrete terms)
    const specificityBonus = this.countSpecificDetails(allText);
    score += specificityBonus * 5;

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Score business specificity
   */
  private static scoreBusinessSpecificity(content: GeneratedContent, businessType: string): number {
    let score = 30; // Base score
    const allText = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();
    const businessTypeLower = businessType.toLowerCase();

    // Find matching business indicators
    const indicators = this.getBusinessIndicators(businessTypeLower);

    let matchCount = 0;
    indicators.forEach(indicator => {
      if (allText.includes(indicator)) {
        matchCount++;
        score += 15; // Bonus for each business-specific term
      }
    });

    // Bonus for business name usage
    if (allText.includes(businessType.toLowerCase())) {
      score += 10;
    }

    // Penalty if no business-specific terms found
    if (matchCount === 0) {
      score -= 20;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Score human-like language patterns
   */
  private static scoreHumanLikeness(content: GeneratedContent): number {
    let score = 50; // Base score
    const allText = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();

    // Check for human conversation patterns
    HUMAN_PATTERNS.forEach(pattern => {
      const matches = allText.match(pattern);
      if (matches) {
        score += matches.length * 8; // Bonus for human patterns
      }
    });

    // Check for contractions (more human-like)
    const contractions = allText.match(/\b\w+'(s|re|ve|ll|d|t)\b/g);
    if (contractions) {
      score += contractions.length * 3;
    }

    // Check for personal pronouns (more engaging)
    const personalPronouns = allText.match(/\b(you|your|we|our|us|i|me)\b/g);
    if (personalPronouns) {
      score += personalPronouns.length * 2;
    }

    // Penalty for overly formal language
    const formalWords = ['utilize', 'facilitate', 'implement', 'optimize', 'enhance'];
    formalWords.forEach(word => {
      if (allText.includes(word)) {
        score -= 8;
      }
    });

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Score cultural relevance
   */
  private static scoreCulturalRelevance(content: GeneratedContent, location: string, useLocalLanguage: boolean): number {
    if (!useLocalLanguage || !location) {
      return 75; // Neutral score if not using local language
    }

    let score = 40; // Base score
    const allText = `${content.headline} ${content.subheadline} ${content.caption}`.toLowerCase();
    const locationLower = location.toLowerCase();

    // Find cultural indicators for location
    const indicators = this.getCulturalIndicators(locationLower);

    indicators.forEach(indicator => {
      if (allText.includes(indicator)) {
        score += 15; // Bonus for cultural elements
      }
    });

    // Bonus for location mention
    if (allText.includes(locationLower)) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Score call-to-action effectiveness
   */
  private static scoreActionability(content: GeneratedContent): number {
    let score = 50; // Base score
    const cta = content.callToAction.toLowerCase();

    // Strong action words
    const strongActions = ['buy', 'order', 'book', 'call', 'visit', 'try', 'start', 'join', 'get', 'shop'];
    const hasStrongAction = strongActions.some(action => cta.includes(action));

    if (hasStrongAction) {
      score += 25;
    }

    // Appropriate length (2-4 words is ideal)
    const wordCount = cta.split(' ').length;
    if (wordCount >= 2 && wordCount <= 4) {
      score += 15;
    } else if (wordCount > 4) {
      score -= 10; // Penalty for too long
    }

    // Penalty for generic CTAs
    const genericCTAs = ['learn more', 'contact us', 'get started', 'find out more'];
    if (genericCTAs.includes(cta)) {
      score -= 15;
    }

    // Bonus for business-specific CTAs
    if (cta.includes('book') || cta.includes('order') || cta.includes('shop') || cta.includes('try')) {
      score += 10;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Count specific details in text
   */
  private static countSpecificDetails(text: string): number {
    let count = 0;

    // Numbers
    const numbers = text.match(/\b\d+\b/g);
    if (numbers) count += numbers.length;

    // Percentages
    const percentages = text.match(/\d+%/g);
    if (percentages) count += percentages.length;

    // Time references
    const timeRefs = text.match(/\b(today|tomorrow|this week|monday|tuesday|wednesday|thursday|friday|saturday|sunday)\b/gi);
    if (timeRefs) count += timeRefs.length;

    // Specific locations
    const locations = text.match(/\b(street|avenue|road|center|mall|downtown|uptown)\b/gi);
    if (locations) count += locations.length;

    return count;
  }

  /**
   * Get business-specific indicators
   */
  private static getBusinessIndicators(businessType: string): string[] {
    for (const [key, indicators] of Object.entries(BUSINESS_SPECIFICITY_INDICATORS)) {
      if (businessType.includes(key) || key.includes(businessType)) {
        return indicators;
      }
    }
    return BUSINESS_SPECIFICITY_INDICATORS['professional services'];
  }

  /**
   * Get cultural indicators for location
   */
  private static getCulturalIndicators(location: string): string[] {
    for (const [key, indicators] of Object.entries(CULTURAL_INDICATORS)) {
      if (location.includes(key)) {
        return indicators;
      }
    }
    return [];
  }

  /**
   * Generate improvement suggestions based on quality score
   */
  static generateImprovementSuggestions(
    qualityScore: ContentQualityScore,
    brandProfile: BrandProfile
  ): string {
    const suggestions: string[] = [];

    if (qualityScore.authenticity < 70) {
      suggestions.push(`Replace generic terms with specific ${brandProfile.businessType} language`);
      suggestions.push('Use concrete details instead of abstract concepts');
    }

    if (qualityScore.businessSpecificity < 60) {
      suggestions.push(`Include specific ${brandProfile.businessType} services or products`);
      suggestions.push('Mention unique business features or specialties');
    }

    if (qualityScore.humanLikeness < 65) {
      suggestions.push('Use more conversational, natural language');
      suggestions.push('Include personal pronouns and contractions');
    }

    if (qualityScore.culturalRelevance < 50) {
      suggestions.push(`Include ${brandProfile.location} community references`);
      suggestions.push('Add culturally relevant elements');
    }

    if (qualityScore.actionability < 70) {
      suggestions.push('Use stronger, more specific action words in CTA');
      suggestions.push('Make the call-to-action more business-specific');
    }

    return suggestions.join('; ');
  }

  /**
   * Check if content needs regeneration
   */
  static needsRegeneration(qualityScore: ContentQualityScore): boolean {
    return qualityScore.shouldRegenerate ||
      qualityScore.overall < 65 ||
      qualityScore.authenticity < 60 ||
      qualityScore.businessSpecificity < 50;
  }

  /**
   * CRITICAL: Validate and fix hashtags to prevent random numbers and fake hashtags
   */
  static validateAndFixHashtags(hashtags: string[], businessType: string, location: string): {
    validHashtags: string[];
    issues: string[];
    fixes: string[];
  } {
    const issues: string[] = [];
    const fixes: string[] = [];
    const validHashtags: string[] = [];

    console.log('🔍 [Content Validator] Validating hashtags:', hashtags);

    for (const hashtag of hashtags) {
      // CRITICAL: Check for random numbers (like #857, #937, #640)
      if (/^#\d+$/.test(hashtag)) {
        issues.push(`❌ Random number hashtag detected: ${hashtag}`);
        fixes.push(`Removed random number hashtag: ${hashtag}`);
        console.warn(`🚨 [Content Validator] BLOCKED random number hashtag: ${hashtag}`);
        continue; // Skip this hashtag completely
      }

      // Check for hashtags with random numbers mixed in (but allow years like #2024)
      if (/^#[a-zA-Z]*\d+[a-zA-Z]*$/.test(hashtag) && !/^#[a-zA-Z]+\d{4}$/.test(hashtag)) {
        issues.push(`❌ Hashtag with random numbers: ${hashtag}`);
        fixes.push(`Removed hashtag with random numbers: ${hashtag}`);
        console.warn(`🚨 [Content Validator] BLOCKED hashtag with random numbers: ${hashtag}`);
        continue; // Skip this hashtag
      }

      // Check for meaningless or AI-generated hashtags
      const meaninglessPatterns = [
        /^#[a-z]{1,2}$/i, // Too short (like #gh, #ke, #ng)
        /^#[qxz]{2,}/i,   // Unlikely letter combinations
        /^#.*[qxz].*[qxz]/i, // Multiple rare letters
        /^#[bcdfghjklmnpqrstvwxyz]{5,}$/i, // Too many consonants
      ];

      let isMeaningless = false;
      for (const pattern of meaninglessPatterns) {
        if (pattern.test(hashtag)) {
          issues.push(`❌ Meaningless hashtag: ${hashtag}`);
          fixes.push(`Removed meaningless hashtag: ${hashtag}`);
          console.warn(`🚨 [Content Validator] BLOCKED meaningless hashtag: ${hashtag}`);
          isMeaningless = true;
          break;
        }
      }

      if (!isMeaningless) {
        validHashtags.push(hashtag);
        console.log(`✅ [Content Validator] Valid hashtag: ${hashtag}`);
      }
    }

    // If we removed too many hashtags, add appropriate ones
    if (validHashtags.length < 3) {
      const replacementHashtags = this.getReplacementHashtags(businessType, location, 5 - validHashtags.length);
      validHashtags.push(...replacementHashtags);
      fixes.push(`Added ${replacementHashtags.length} appropriate replacement hashtags: ${replacementHashtags.join(', ')}`);
      console.log(`🔄 [Content Validator] Added replacement hashtags:`, replacementHashtags);
    }

    console.log(`✅ [Content Validator] Final valid hashtags:`, validHashtags);
    return { validHashtags, issues, fixes };
  }

  /**
   * CRITICAL: Validate and fix headlines to prevent unclear messaging and hallucinations
   */
  static validateAndFixHeadline(headline: string, businessType: string, location: string): {
    validHeadline: string;
    issues: string[];
    fixes: string[];
  } {
    const issues: string[] = [];
    const fixes: string[] = [];
    let validHeadline = headline;

    console.log('🔍 [Content Validator] Validating headline:', headline);

    // CRITICAL: Check for embedded random number hashtags in headline text (like "#981", "#634", "#895")
    const randomNumberHashtagPattern = /#\d+/g;
    const embeddedHashtags = validHeadline.match(randomNumberHashtagPattern);
    if (embeddedHashtags) {
      for (const hashtag of embeddedHashtags) {
        issues.push(`❌ Random number hashtag embedded in headline: "${hashtag}"`);
        console.warn(`🚨 [Content Validator] BLOCKED embedded random hashtag: "${hashtag}"`);
        validHeadline = validHeadline.replace(hashtag, '').trim();
        fixes.push(`Removed embedded random hashtag "${hashtag}" from headline`);
      }
    }

    // CRITICAL: Check for unclear technical jargon from user feedback
    const problematicPhrases = [
      'Produce Financial Technology Breakthrough GH',
      'Produce Financial Technology Breakthrough',
      'Financial Technology Breakthrough GH',
      'Financial Technology Breakthrough',
      'Technology Breakthrough GH',
      'Technology Breakthrough',
      'Breakthrough GH',
      'Breakthrough KE',
      'Breakthrough NG',
      'Breakthrough ZA'
    ];

    for (const phrase of problematicPhrases) {
      if (validHeadline.includes(phrase)) {
        issues.push(`❌ Unclear technical jargon: "${phrase}"`);
        console.warn(`🚨 [Content Validator] BLOCKED unclear jargon: "${phrase}"`);

        // Replace with clear, benefit-focused language
        if (businessType.toLowerCase().includes('financial') || businessType.toLowerCase().includes('fintech')) {
          validHeadline = this.getFinancialHeadline(location);
          fixes.push(`Replaced with clear financial benefit: "${validHeadline}"`);
        } else {
          validHeadline = this.getClearBusinessHeadline(businessType, location);
          fixes.push(`Replaced with clear business benefit: "${validHeadline}"`);
        }
        break;
      }
    }

    // CRITICAL: Check for hallucinated words from user feedback
    const hallucinatedWords = ['Tephradoqy', 'Technologhy', 'Breakthroughy', 'Innovationy', 'Solutiony'];
    for (const word of hallucinatedWords) {
      if (validHeadline.includes(word)) {
        issues.push(`❌ Hallucinated word detected: "${word}"`);
        console.warn(`🚨 [Content Validator] BLOCKED hallucinated word: "${word}"`);
        validHeadline = validHeadline.replace(word, 'Service');
        fixes.push(`Replaced hallucinated word "${word}" with "Service"`);
      }
    }

    // Ensure headline is clear and under 6 words
    const wordCount = validHeadline.split(' ').length;
    if (wordCount > 6) {
      issues.push(`❌ Headline too long: ${wordCount} words (max 6)`);
      validHeadline = this.shortenHeadline(validHeadline, businessType, location);
      fixes.push(`Shortened headline to: "${validHeadline}"`);
    }

    console.log(`✅ [Content Validator] Final valid headline: "${validHeadline}"`);
    return { validHeadline, issues, fixes };
  }

  /**
   * Get replacement hashtags for removed invalid ones
   */
  private static getReplacementHashtags(businessType: string, location: string, count: number): string[] {
    const locationHashtags: Record<string, string[]> = {
      'kenya': ['#Kenya', '#Nairobi', '#KenyanBusiness', '#EastAfrica', '#Mombasa'],
      'nigeria': ['#Nigeria', '#Lagos', '#NigerianBusiness', '#WestAfrica', '#Abuja'],
      'ghana': ['#Ghana', '#Accra', '#GhanaianBusiness', '#WestAfrica', '#Kumasi'],
      'south africa': ['#SouthAfrica', '#Johannesburg', '#SouthAfricanBusiness', '#CapeTown'],
      'india': ['#India', '#Mumbai', '#IndianBusiness', '#Delhi', '#Bangalore']
    };

    const businessHashtags: Record<string, string[]> = {
      'financial': ['#FinancialServices', '#Banking', '#MobilePayments', '#FinTech', '#SecurePayments'],
      'restaurant': ['#Restaurant', '#Food', '#Dining', '#LocalEats', '#FreshFood'],
      'retail': ['#Shopping', '#Retail', '#Fashion', '#LocalBusiness', '#Quality'],
      'technology': ['#Technology', '#Innovation', '#DigitalServices', '#TechSolutions', '#Reliable']
    };

    const hashtags: string[] = [];

    // Add location-based hashtags
    const locationKey = location.toLowerCase();
    const locationTags = locationHashtags[locationKey] || ['#LocalBusiness', '#Community'];
    hashtags.push(...locationTags.slice(0, Math.ceil(count / 2)));

    // Add business-type hashtags
    const businessKey = Object.keys(businessHashtags).find(key => businessType.toLowerCase().includes(key)) || 'business';
    const businessTags = businessHashtags[businessKey] || ['#Business', '#Professional', '#Quality'];
    hashtags.push(...businessTags.slice(0, Math.floor(count / 2)));

    return hashtags.slice(0, count);
  }

  /**
   * Get clear financial headline
   */
  private static getFinancialHeadline(location: string): string {
    const headlines = [
      'Secure Mobile Payments',
      'Send Money Instantly',
      'Grow Your Business',
      'Safe Digital Banking',
      'Trusted Financial Services',
      'Fast Money Transfer',
      'Reliable Banking Solutions'
    ];
    return headlines[Math.floor(Math.random() * headlines.length)];
  }

  /**
   * Get clear business headline
   */
  private static getClearBusinessHeadline(businessType: string, location: string): string {
    return `Quality ${businessType} Services`;
  }

  /**
   * Shorten headline to 6 words or less
   */
  private static shortenHeadline(headline: string, businessType: string, location: string): string {
    const words = headline.split(' ');
    if (words.length <= 6) return headline;

    // Try to keep the most important words
    const importantWords = words.filter(word =>
      !['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'].includes(word.toLowerCase())
    );

    if (importantWords.length <= 6) {
      return importantWords.join(' ');
    }

    return importantWords.slice(0, 6).join(' ');
  }
}
