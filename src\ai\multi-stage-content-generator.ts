/**
 * Multi-Stage Content Generator for Revo 1.5
 * Implements strategic analysis → theme selection → content generation → validation pipeline
 */

import type { BrandProfile, Platform } from '@/lib/types';
import type { ScheduledService } from '@/services/calendar-service';
import { EnhancedPromptBuilder, type PromptBuilderContext } from './enhanced-prompt-builder';
import { BusinessIntelligenceEngine, type BusinessIntelligence } from './business-intelligence-engine';
import { ContentQualityValidator, type ContentQualityScore, type GeneratedContent } from './content-quality-validator';
import { ContentDiversificationEngine, type ContentVariationContext, type DiversificationStrategy } from './content-diversification-engine';
import { getVertexAIClient } from '@/lib/services/vertex-ai-client';

export interface ContentGenerationContext {
  brandProfile: BrandProfile;
  platform: Platform;
  scheduledServices?: ScheduledService[];
  realTimeContext?: any;
  useLocalLanguage?: boolean;
  maxRegenerationAttempts?: number;
  sessionId?: string; // For content diversification tracking
}

export interface ContentGenerationResult {
  content: GeneratedContent;
  qualityScore: ContentQualityScore;
  businessIntelligence: BusinessIntelligence;
  strategicAnalysis: StrategicAnalysis;
  contentTheme: ContentTheme;
  diversificationStrategy: DiversificationStrategy;
  processingSteps: string[];
  regenerationAttempts: number;
  success: boolean;
}

export interface StrategicAnalysis {
  primaryObjective: string;
  targetAudience: string;
  keyMessage: string;
  emotionalTone: string;
  competitiveAdvantage: string;
  urgencyFactor: string;
}

export interface ContentTheme {
  theme: string;
  angle: string;
  framework: string;
  focusAreas: string[];
  messagingStrategy: string;
}



export class MultiStageContentGenerator {
  /**
   * Generate content using multi-stage pipeline
   */
  static async generateContent(context: ContentGenerationContext): Promise<ContentGenerationResult> {
    const processingSteps: string[] = [];
    const maxAttempts = context.maxRegenerationAttempts || 3;
    let regenerationAttempts = 0;

    try {
      // Stage 1: Strategic Analysis
      processingSteps.push('Stage 1: Strategic Business Analysis');
      const businessIntelligence = BusinessIntelligenceEngine.generateIntelligence(
        context.brandProfile,
        context.scheduledServices,
        context.realTimeContext
      );

      const strategicAnalysis = await this.performStrategicAnalysis(context, businessIntelligence);
      processingSteps.push('✓ Strategic analysis completed');

      // Content Diversification Strategy
      processingSteps.push('Applying Content Diversification Strategy');
      const variationContext: ContentVariationContext = {
        businessType: context.brandProfile.businessType || 'professional services',
        location: context.brandProfile.location || 'Kenya',
        platform: context.platform,
        sessionId: context.sessionId || `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
      };

      const diversificationStrategy = ContentDiversificationEngine.getDiversificationStrategy(variationContext);
      processingSteps.push(`✓ Diversification strategy: ${diversificationStrategy.themeRotation[0]} theme with ${diversificationStrategy.messagingAngles[0]} angle`);

      // Stage 2: Content Theme Selection
      processingSteps.push('Stage 2: Content Theme & Messaging Strategy');
      const contentTheme = await this.selectContentTheme(context, businessIntelligence, strategicAnalysis, diversificationStrategy);
      processingSteps.push('✓ Content theme selected');

      // Stage 3: Content Generation with Validation Loop
      processingSteps.push('Stage 3: Content Generation & Quality Validation');
      let content: GeneratedContent;
      let qualityScore: ContentQualityScore;

      do {
        content = await this.generateContentWithTheme(context, businessIntelligence, contentTheme, diversificationStrategy);
        qualityScore = ContentQualityValidator.validateContent(
          content,
          context.brandProfile,
          context.useLocalLanguage
        );

        regenerationAttempts++;

        if (qualityScore.shouldRegenerate && regenerationAttempts < maxAttempts) {
          processingSteps.push(`⚠ Quality score: ${qualityScore.overall}% - Regenerating (attempt ${regenerationAttempts})`);
          // Add improvement context for next attempt
          contentTheme.messagingStrategy += ` | Avoid: ${qualityScore.issues.join(', ')}`;
        } else {
          processingSteps.push(`✓ Content generated with quality score: ${qualityScore.overall}%`);
          break;
        }
      } while (regenerationAttempts < maxAttempts);

      // Stage 4: Final Validation
      processingSteps.push('Stage 4: Final Quality Assessment');
      const finalQualityScore = ContentQualityValidator.validateContent(
        content,
        context.brandProfile,
        context.useLocalLanguage
      );

      processingSteps.push(`✓ Final quality score: ${finalQualityScore.overall}%`);

      return {
        content,
        qualityScore: finalQualityScore,
        businessIntelligence,
        strategicAnalysis,
        contentTheme,
        diversificationStrategy,
        processingSteps,
        regenerationAttempts,
        success: true
      };

    } catch (error) {
      processingSteps.push(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Return fallback content
      return {
        content: this.generateFallbackContent(context),
        qualityScore: {
          overall: 50,
          authenticity: 50,
          businessSpecificity: 50,
          humanLikeness: 50,
          culturalRelevance: 50,
          actionability: 50,
          issues: ['Generation failed - using fallback'],
          recommendations: ['Retry generation'],
          shouldRegenerate: true
        },
        businessIntelligence: BusinessIntelligenceEngine.generateIntelligence(context.brandProfile),
        strategicAnalysis: {
          primaryObjective: 'Brand awareness',
          targetAudience: context.brandProfile.targetAudience || 'Local community',
          keyMessage: 'Quality service provider',
          emotionalTone: 'Professional and trustworthy',
          competitiveAdvantage: 'Local expertise',
          urgencyFactor: 'Available now'
        },
        contentTheme: {
          theme: 'Professional service',
          angle: 'Quality focus',
          framework: 'Benefit-focused',
          focusAreas: ['quality', 'service', 'trust'],
          messagingStrategy: 'Highlight professional expertise'
        },
        diversificationStrategy: {
          themeRotation: ['Benefit-Focused'],
          messagingAngles: ['Quality Provider'],
          ctaVariations: ['Contact Us'],
          creativeFocus: ['Service-Highlight'],
          tonalVariations: ['Professional & Trustworthy']
        },
        processingSteps,
        regenerationAttempts,
        success: false
      };
    }
  }

  /**
   * Stage 1: Perform strategic business analysis
   */
  private static async performStrategicAnalysis(
    context: ContentGenerationContext,
    businessIntelligence: BusinessIntelligence
  ): Promise<StrategicAnalysis> {
    const analysisPrompt = `As a strategic marketing consultant, analyze this business for social media content strategy:

BUSINESS PROFILE:
- Company: ${context.brandProfile.businessName}
- Industry: ${context.brandProfile.businessType}
- Location: ${context.brandProfile.location}
- Target Audience: ${context.brandProfile.targetAudience || 'Local community'}
- Services: ${context.brandProfile.services || 'Professional services'}

BUSINESS INTELLIGENCE:
${BusinessIntelligenceEngine.getInsightsSummary(businessIntelligence)}

PLATFORM: ${context.platform}

Provide strategic analysis for ${context.platform} content creation:

1. PRIMARY OBJECTIVE: What should this content achieve? (brand awareness, lead generation, customer retention, etc.)
2. TARGET AUDIENCE: Who specifically should this content reach and resonate with?
3. KEY MESSAGE: What single most important message should this content communicate?
4. EMOTIONAL TONE: What emotional response should this content evoke?
5. COMPETITIVE ADVANTAGE: What unique value should be highlighted?
6. URGENCY FACTOR: What creates urgency or immediate interest?

Respond in JSON format:
{
  "primaryObjective": "specific objective",
  "targetAudience": "specific audience description",
  "keyMessage": "core message",
  "emotionalTone": "emotional approach",
  "competitiveAdvantage": "unique differentiator",
  "urgencyFactor": "urgency element"
}`;

    try {
      const result = await getVertexAIClient().generateText(analysisPrompt, 'gemini-2.5-flash', {
        temperature: 0.3, // Lower temperature for analytical content
        maxOutputTokens: 1000
      });

      const response = result.text;
      const jsonMatch = response.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.warn('Strategic analysis failed, using fallback:', error);
    }

    // Fallback strategic analysis
    return {
      primaryObjective: 'Build brand awareness and attract local customers',
      targetAudience: context.brandProfile.targetAudience || 'Local community seeking quality services',
      keyMessage: `Professional ${context.brandProfile.businessType} services in ${context.brandProfile.location}`,
      emotionalTone: 'Trustworthy and professional with local warmth',
      competitiveAdvantage: 'Local expertise and personalized service',
      urgencyFactor: 'Available for immediate service'
    };
  }

  /**
   * Stage 2: Select optimal content theme and messaging strategy
   */
  private static async selectContentTheme(
    context: ContentGenerationContext,
    businessIntelligence: BusinessIntelligence,
    strategicAnalysis: StrategicAnalysis,
    diversificationStrategy: DiversificationStrategy
  ): Promise<ContentTheme> {
    // Use diversification strategy to ensure content variation
    const selectedTheme = diversificationStrategy.themeRotation[0];
    const messagingAngle = diversificationStrategy.messagingAngles[0];
    const creativeFocus = diversificationStrategy.creativeFocus[0];
    const tonalVariation = diversificationStrategy.tonalVariations[0];

    const themePrompt = `As a content strategist, create a content theme using the REQUIRED diversification strategy:

REQUIRED CONTENT STRATEGY (MUST FOLLOW):
- Theme: ${selectedTheme}
- Messaging Angle: ${messagingAngle}
- Creative Focus: ${creativeFocus}
- Tonal Variation: ${tonalVariation}

STRATEGIC ANALYSIS:
- Objective: ${strategicAnalysis.primaryObjective}
- Audience: ${strategicAnalysis.targetAudience}
- Key Message: ${strategicAnalysis.keyMessage}
- Emotional Tone: ${strategicAnalysis.emotionalTone}
- Competitive Advantage: ${strategicAnalysis.competitiveAdvantage}

BUSINESS CONTEXT:
- Business: ${context.brandProfile.businessName} (${context.brandProfile.businessType})
- Platform: ${context.platform}
${context.scheduledServices?.length ? `- Today's Services: ${context.scheduledServices.filter(s => s.isToday).map(s => s.serviceName).join(', ')}` : ''}

${ContentDiversificationEngine.getVariationInstructions(diversificationStrategy)}

Create a messaging strategy that implements the ${selectedTheme} theme with ${messagingAngle} positioning:

Respond in JSON format:
{
  "theme": "selected theme name",
  "angle": "specific angle or approach",
  "framework": "marketing framework to use",
  "focusAreas": ["area1", "area2", "area3"],
  "messagingStrategy": "detailed messaging approach"
}`;

    try {
      const result = await getVertexAIClient().generateText(themePrompt, 'gemini-2.5-flash', {
        temperature: 0.4,
        maxOutputTokens: 800
      });

      const response = result.text;
      const jsonMatch = response.match(/\{[\s\S]*\}/);

      if (jsonMatch) {
        return JSON.parse(jsonMatch[0]);
      }
    } catch (error) {
      console.warn('Theme selection failed, using fallback:', error);
    }

    // Fallback theme selection
    return {
      theme: 'Benefit-Focused',
      angle: 'Quality service emphasis',
      framework: 'AIDA',
      focusAreas: ['quality', 'expertise', 'local service'],
      messagingStrategy: 'Emphasize professional expertise and local community connection'
    };
  }

  /**
   * Stage 3: Generate content using selected theme and strategy
   */
  private static async generateContentWithTheme(
    context: ContentGenerationContext,
    businessIntelligence: BusinessIntelligence,
    contentTheme: ContentTheme,
    diversificationStrategy: DiversificationStrategy
  ): Promise<GeneratedContent> {
    // Build enhanced prompt using the prompt builder
    const promptContext: PromptBuilderContext = {
      brandProfile: context.brandProfile,
      platform: context.platform,
      scheduledServices: context.scheduledServices,
      realTimeContext: context.realTimeContext,
      useLocalLanguage: context.useLocalLanguage
    };

    const basePrompt = EnhancedPromptBuilder.buildContentGenerationPrompt(promptContext);

    // Enhance with theme-specific guidance and diversification instructions
    const themeEnhancedPrompt = `${basePrompt}

CONTENT THEME & STRATEGY:
- Theme: ${contentTheme.theme}
- Angle: ${contentTheme.angle}
- Framework: ${contentTheme.framework}
- Focus Areas: ${contentTheme.focusAreas.join(', ')}
- Messaging Strategy: ${contentTheme.messagingStrategy}

${ContentDiversificationEngine.getVariationInstructions(diversificationStrategy)}

SPECIFIC REQUIREMENTS FOR THIS THEME:
${this.getThemeSpecificRequirements(contentTheme.theme)}

Generate content that perfectly aligns with this theme and strategy.`;

    try {
      const result = await getVertexAIClient().generateText(themeEnhancedPrompt, 'gemini-2.5-flash', {
        temperature: 0.7,
        maxOutputTokens: 2000
      });

      const response = result.text;
      let jsonContent = response;

      // Clean up response
      if (jsonContent.includes('```json')) {
        jsonContent = jsonContent.split('```json')[1]?.split('```')[0] || jsonContent;
      } else if (jsonContent.includes('```')) {
        jsonContent = jsonContent.split('```')[1] || jsonContent;
      }

      const jsonMatch = jsonContent.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        return {
          headline: parsed.headline || 'Quality Service',
          subheadline: parsed.subheadline || 'Professional results you can trust',
          caption: parsed.caption || `${context.brandProfile.businessName} provides quality ${context.brandProfile.businessType} services.`,
          callToAction: parsed.callToAction || 'Contact Us',
          hashtags: parsed.hashtags || ['#business', '#local', '#quality']
        };
      }
    } catch (error) {
      console.warn('Content generation failed, using fallback');
    }

    return this.generateFallbackContent(context);
  }

  /**
   * Get theme-specific requirements
   */
  private static getThemeSpecificRequirements(theme: string): string {
    const requirements = {
      'Problem-Solution': 'Start with a relatable problem, agitate it briefly, then present your service as the clear solution',
      'Benefit-Focused': 'Lead with the strongest benefit, support with specific details, and make it personally relevant',
      'Social-Proof': 'Include community trust indicators, success stories, or local reputation elements',
      'Behind-the-Scenes': 'Show authentic business operations, expertise in action, or team dedication',
      'Community-Connection': 'Emphasize local relationships, community involvement, and neighborhood presence',
      'Educational': 'Share valuable insights, tips, or expertise that positions you as the local authority',
      'Seasonal-Timely': 'Connect with current events, seasons, or timely needs in your community'
    };

    return requirements[theme] || requirements['Benefit-Focused'];
  }

  /**
   * Generate fallback content when AI generation fails
   */
  private static generateFallbackContent(context: ContentGenerationContext): GeneratedContent {
    const { brandProfile, platform } = context;
    const hashtagCount = platform.toLowerCase() === 'instagram' ? 5 : 3;

    return {
      headline: `${brandProfile.businessName} Quality`,
      subheadline: `Professional ${brandProfile.businessType} services in ${brandProfile.location}`,
      caption: `${brandProfile.businessName} is your trusted ${brandProfile.businessType} partner in ${brandProfile.location}. We're committed to providing excellent service and results that exceed your expectations.`,
      callToAction: 'Contact Us',
      hashtags: [
        `#${brandProfile.businessType.toLowerCase().replace(/\s+/g, '')}`,
        '#local',
        '#business',
        '#quality',
        '#professional'
      ].slice(0, hashtagCount)
    };
  }
}
