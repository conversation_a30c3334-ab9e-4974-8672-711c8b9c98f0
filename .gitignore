# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/.next-alt/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.genkit/*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# firebase
firebase-debug.log
firestore-debug.log
*firebase-adminsdk*.json
service-account-key.json

# vertex ai credentials
vertex-ai-credentials*.json
*vertex-ai-credentials*.json