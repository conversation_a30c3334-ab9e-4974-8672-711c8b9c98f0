/**
 * Simple Content Diversifier - Direct implementation without complex AI analysis
 * Fixes the repetitive content issue by ensuring variation in every generation
 */

import type { BrandProfile, Platform } from '@/lib/types';
import type { ScheduledService } from '@/services/calendar-service';

export interface SimpleDiversificationResult {
  theme: string;
  messagingAngle: string;
  creativeFocus: string;
  tonalVariation: string;
  ctaVariation: string;
  variationInstructions: string;
}

export class SimpleContentDiversifier {
  private static usedCombinations: Set<string> = new Set();

  /**
   * Get diversified content parameters to ensure variation
   */
  static getDiversifiedParameters(
    brandProfile: BrandProfile,
    platform: Platform,
    sessionId?: string
  ): SimpleDiversificationResult {

    // Customer-outcome focused themes (what customers GET, not what we ARE)
    const themes = [
      'Problem-Solution', // "Struggling with X? Here's how we solve it"
      'Results-Focused', // "Get X result in Y time"
      'Social-Proof', // "Join 500+ businesses who achieved X"
      'Time-Savings', // "Save X hours/days with our solution"
      'Cost-Reduction', // "Cut costs by X% while improving Y"
      'Revenue-Growth', // "Increase revenue by X% in Y months"
      'Risk-Mitigation', // "Avoid costly mistakes with our expertise"
      'Competitive-Advantage', // "Stay ahead of competitors with X"
      'Efficiency-Boost', // "3x faster results with our system"
      'Peace-of-Mind', // "Sleep better knowing X is handled"
      'Growth-Enabler', // "Scale your business without the headaches"
      'Transformation-Story' // "From struggling to thriving in X months"
    ];

    // Customer-benefit focused messaging angles
    const messagingAngles = [
      'Proven Results', // "2,500+ businesses achieved X"
      'Time-Saver', // "Get results in half the time"
      'Cost-Effective', // "Same results, 50% less cost"
      'Risk-Free', // "Guaranteed results or money back"
      'Exclusive Access', // "Limited to 100 businesses per month"
      'Local Expert', // "Only provider in [location] with X certification"
      'Fast Implementation', // "Up and running in 24 hours"
      'Scalable Solution', // "Grows with your business"
      'Hassle-Free', // "We handle everything for you"
      'Competitive Edge' // "What your competitors don't want you to know"
    ];

    // Customer-outcome focused creative approaches
    const creativeFoci = [
      'Before-After', // Show transformation/improvement
      'Numbers-Driven', // Specific metrics and results
      'Customer-Stories', // Real success testimonials
      'Problem-Agitation', // Highlight pain points then solve
      'Benefit-Stacking', // Multiple value propositions
      'Urgency-Creation', // Limited time/spots available
      'Social-Proof', // "Join thousands who already..."
      'Comparison-Based', // "Unlike other providers..."
      'Guarantee-Focused', // Risk reversal and assurance
      'Exclusive-Access' // VIP/insider positioning
    ];

    // Business-specific tonal variations
    const businessType = brandProfile.businessType || 'professional services';
    const tonalVariations = this.getTonalVariations(businessType);

    // Business-specific CTAs
    const ctaVariations = this.getCTAVariations(businessType);

    // Generate unique combination
    const timestamp = Date.now();
    const randomSeed = Math.floor(Math.random() * 1000);
    const combinationKey = `${brandProfile.businessName}-${timestamp}-${randomSeed}`;

    // Select random elements to ensure variation
    const selectedTheme = this.selectRandomItem(themes);
    const selectedAngle = this.selectRandomItem(messagingAngles);
    const selectedFocus = this.selectRandomItem(creativeFoci);
    const selectedTone = this.selectRandomItem(tonalVariations);
    const selectedCTA = this.selectRandomItem(ctaVariations);

    // Track this combination
    this.usedCombinations.add(combinationKey);

    // Generate variation instructions
    const variationInstructions = this.generateVariationInstructions(
      selectedTheme,
      selectedAngle,
      selectedFocus,
      selectedTone,
      selectedCTA,
      brandProfile
    );

    return {
      theme: selectedTheme,
      messagingAngle: selectedAngle,
      creativeFocus: selectedFocus,
      tonalVariation: selectedTone,
      ctaVariation: selectedCTA,
      variationInstructions
    };
  }

  /**
   * Get business-specific tonal variations
   */
  private static getTonalVariations(businessType: string): string[] {
    const tonalMap = {
      restaurant: ['Warm & Inviting', 'Exciting & Appetizing', 'Cozy & Familiar', 'Premium & Sophisticated'],
      'professional services': ['Professional & Trustworthy', 'Expert & Authoritative', 'Approachable & Helpful', 'Innovative & Forward-thinking'],
      retail: ['Trendy & Exciting', 'Value-Focused', 'Quality-Assured', 'Customer-Centric'],
      healthcare: ['Caring & Compassionate', 'Professional & Reliable', 'Advanced & Innovative', 'Community-Focused'],
      fitness: ['Motivational & Energetic', 'Supportive & Encouraging', 'Results-Oriented', 'Community-Driven'],
      beauty: ['Luxurious & Pampering', 'Transformative & Confident', 'Natural & Authentic', 'Trendy & Stylish'],
      technology: ['Innovative & Cutting-edge', 'Reliable & Secure', 'User-Friendly', 'Future-Forward']
    };

    return tonalMap[businessType] || tonalMap['professional services'];
  }

  /**
   * Get business-specific CTA variations
   */
  private static getCTAVariations(businessType: string): string[] {
    const ctaMap = {
      restaurant: ['Order Now', 'Book Table', 'View Menu', 'Call Now', 'Visit Today', 'Try Our Special', 'Reserve Seat'],
      'professional services': ['Get Quote', 'Schedule Consultation', 'Contact Expert', 'Learn More', 'Start Today', 'Book Appointment'],
      retail: ['Shop Now', 'Browse Collection', 'Get Yours', 'Limited Offer', 'Buy Today', 'Explore Range'],
      healthcare: ['Book Appointment', 'Schedule Checkup', 'Contact Clinic', 'Get Care', 'Call Doctor', 'Visit Today'],
      fitness: ['Start Training', 'Join Gym', 'Book Session', 'Get Fit', 'Try Class', 'Transform Today'],
      beauty: ['Book Treatment', 'Get Pampered', 'Transform Look', 'Visit Salon', 'Try Service', 'Look Amazing'],
      technology: ['Get Solution', 'Start Free Trial', 'Contact Sales', 'Learn More', 'Upgrade Now', 'Get Demo']
    };

    return ctaMap[businessType] || ctaMap['professional services'];
  }

  /**
   * Generate comprehensive variation instructions
   */
  private static generateVariationInstructions(
    theme: string,
    angle: string,
    focus: string,
    tone: string,
    cta: string,
    brandProfile: BrandProfile
  ): string {
    return `
MANDATORY CONTENT VARIATION REQUIREMENTS:

PRIMARY APPROACH:
- Theme: ${theme} (MUST be the primary content approach)
- Messaging Angle: ${angle} (MUST position the business this way)
- Creative Focus: ${focus} (MUST be the visual/content focus)
- Tone: ${tone} (MUST adopt this emotional tone)
- CTA Style: ${cta} (MUST use this type of call-to-action)

UNIQUENESS MANDATE:
- Create COMPLETELY UNIQUE content that differs from any previous generation
- Use FRESH vocabulary and avoid repetitive phrases
- Develop ORIGINAL headlines that haven't been used before
- Create DISTINCTIVE messaging that stands out
- Ensure EVERY element (headline, subheadline, CTA) feels new and different

BUSINESS CONTEXT:
- Business: ${brandProfile.businessName}
- Type: ${brandProfile.businessType}
- Location: ${brandProfile.location}

VARIATION ENFORCEMENT:
- NO generic marketing speak or templates
- NO repetitive patterns or formulaic language
- MUST sound natural and conversational
- MUST reflect the specific ${angle} positioning
- MUST maintain ${tone} throughout all content
- MUST focus on ${focus} perspective in messaging

CONTENT FRESHNESS:
- Use varied sentence structures
- Employ different vocabulary choices
- Create unique value propositions
- Develop original emotional hooks
- Ensure distinctive brand voice expression

This is generation #${Date.now()} - ensure it's completely different from any previous content.
    `.trim();
  }

  /**
   * Select random item from array
   */
  private static selectRandomItem<T>(items: T[]): T {
    return items[Math.floor(Math.random() * items.length)];
  }

  /**
   * Clear used combinations (for testing)
   */
  static clearUsedCombinations(): void {
    this.usedCombinations.clear();
  }

  /**
   * Get stats on used combinations
   */
  static getUsageStats(): { totalCombinations: number } {
    return {
      totalCombinations: this.usedCombinations.size
    };
  }
}
