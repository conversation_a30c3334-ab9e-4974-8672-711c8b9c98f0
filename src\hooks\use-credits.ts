"use client";

import { useState, useCallback } from 'react';
import { useAuth } from '@/hooks/use-auth-supabase';

export interface UserCredits {
  total_credits: number;
  remaining_credits: number;
  used_credits: number;
  last_payment_at?: string | null;
}

export interface UseCreditResult {
  success: boolean;
  credits_used?: number;
  model_version?: string;
  remaining_credits?: number;
  used_credits?: number;
  error?: string;
}

// Model cost configuration - Revo versions
export const MODEL_COSTS = {
  'revo-1.0': 2,
  'revo-1.5': 3,
  'revo-2.0': 4,
} as const;

export type ModelVersion = keyof typeof MODEL_COSTS;

export function useCredits() {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [creditBalance, setCreditBalance] = useState<UserCredits | null>(null);

  // Check if user has enough credits for a specific model
  const hasEnoughCreditsForModel = useCallback(async (modelVersion: ModelVersion): Promise<boolean> => {
    if (!user) return false;
    
    try {
      // Import the credit integration function dynamically to avoid SSR issues
      const { hasEnoughCreditsForModel: checkCredits } = await import('@/lib/credit-integration');
      const result = await checkCredits(user.userId, modelVersion);
      
      // Update local state with current balance
      if (result.remainingCredits !== undefined) {
        setCreditBalance(prev => prev ? {
          ...prev,
          remaining_credits: result.remainingCredits,
        } : {
          total_credits: result.remainingCredits,
          remaining_credits: result.remainingCredits,
          used_credits: 0,
        });
      }
      
      return result.hasCredits;
    } catch (error) {
      console.error('Error checking credits:', error);
      return false;
    }
  }, [user]);

  // Legacy function for backward compatibility
  const hasEnoughCredits = useCallback(async (requiredCredits: number): Promise<boolean> => {
    if (!user) return false;

    try {
      const response = await fetch(`/api/user/credits?t=${Date.now()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
        credentials: 'include',
      });

      if (!response.ok) return false;

      const credits = await response.json();
      setCreditBalance(credits);
      return credits.remaining_credits >= requiredCredits;
    } catch (error) {
      console.error('Error checking credits:', error);
      return false;
    }
  }, [user]);

  // Use credits for a specific model generation
  const useCreditsForModel = useCallback(async (
    modelVersion: ModelVersion,
    feature: string = 'design_generation',
    generationType: string = 'image'
  ): Promise<UseCreditResult> => {
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    setLoading(true);

    try {
      // Import the credit integration function dynamically to avoid SSR issues
      const { deductCreditsForGeneration } = await import('@/lib/credit-integration');
      
      const result = await deductCreditsForGeneration({
        userId: user.userId,
        modelVersion,
        feature: feature as any,
        generationType,
      });

      if (result.success) {
        // Update local credit balance
        setCreditBalance(prev => prev ? {
          ...prev,
          remaining_credits: result.remainingCredits || 0,
          used_credits: (prev.used_credits || 0) + (result.costDeducted || 0),
        } : null);

        return {
          success: true,
          credits_used: result.costDeducted,
          model_version: modelVersion,
          remaining_credits: result.remainingCredits,
          used_credits: (creditBalance?.used_credits || 0) + (result.costDeducted || 0),
        };
      } else {
        return { 
          success: false, 
          error: result.message || 'Failed to use credits' 
        };
      }
    } catch (error) {
      console.error('Error using credits:', error);
      return { 
        success: false, 
        error: 'Network error occurred' 
      };
    } finally {
      setLoading(false);
    }
  }, [user, creditBalance]);

  // Legacy function for backward compatibility
  const useCredits = useCallback(async (
    creditsToUse: number,
    feature: string = 'Content Generation',
    details?: any
  ): Promise<{ success: boolean; error?: string }> => {
    if (!user) {
      return { success: false, error: 'User not authenticated' };
    }

    setLoading(true);

    try {
      // For legacy usage, default to revo-2.0 if credits match the cost
      let modelVersion: ModelVersion = 'revo-2.0';
      if (creditsToUse === 2) modelVersion = 'revo-1.0';
      else if (creditsToUse === 3) modelVersion = 'revo-1.5';
      else if (creditsToUse === 4) modelVersion = 'revo-2.0';

      const result = await useCreditsForModel(modelVersion, feature);
      return { 
        success: result.success, 
        error: result.error 
      };
    } catch (error) {
      console.error('Error using credits:', error);
      return { 
        success: false, 
        error: 'Network error occurred' 
      };
    } finally {
      setLoading(false);
    }
  }, [user, useCreditsForModel]);

  // Get current credit balance with real-time data
  const getCreditBalance = useCallback(async (): Promise<UserCredits | null> => {
    if (!user) return null;

    try {
      const response = await fetch(`/api/user/credits?t=${Date.now()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache',
        },
        credentials: 'include',
      });

      if (!response.ok) return null;

      const credits = await response.json();
      setCreditBalance(credits);
      return credits;
    } catch (error) {
      console.error('Error getting credit balance:', error);
      return null;
    }
  }, [user]);

  // Add credits (typically called after payment)
  const addCredits = useCallback(async (creditsToAdd: number): Promise<boolean> => {
    if (!user) return false;

    try {
      const response = await fetch('/api/user/credits', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          creditsToAdd,
        }),
      });

      if (response.ok) {
        // Refresh credit balance after adding credits
        await getCreditBalance();
      }

      return response.ok;
    } catch (error) {
      console.error('Error adding credits:', error);
      return false;
    }
  }, [user, getCreditBalance]);

  // Get cost for a specific model
  const getCostForModel = useCallback((modelVersion: ModelVersion): number => {
    return MODEL_COSTS[modelVersion];
  }, []);

  // Check if user can afford a specific model
  const canAffordModel = useCallback(async (modelVersion: ModelVersion): Promise<boolean> => {
    return await hasEnoughCreditsForModel(modelVersion);
  }, [hasEnoughCreditsForModel]);

  // Get number of generations available for a model
  const getGenerationsAvailable = useCallback((modelVersion: ModelVersion): number => {
    const cost = MODEL_COSTS[modelVersion];
    const available = creditBalance?.remaining_credits || 0;
    return Math.floor(available / cost);
  }, [creditBalance]);

  // Get current credit balance
  const getCurrentBalance = useCallback(async (): Promise<number> => {
    if (!user) return 0;
    
    try {
      const { getUserCreditBalance } = await import('@/lib/credit-integration');
      const balance = await getUserCreditBalance(user.userId);
      
      // Update local state
      setCreditBalance(prev => prev ? {
        ...prev,
        remaining_credits: balance,
      } : {
        total_credits: balance,
        remaining_credits: balance,
        used_credits: 0,
      });
      
      return balance;
    } catch (error) {
      console.error('Error getting current balance:', error);
      return 0;
    }
  }, [user]);

  return {
    // Legacy functions for backward compatibility
    hasEnoughCredits,
    useCredits,
    getCreditBalance,
    addCredits,
    loading,
    
    // New model-specific functions
    hasEnoughCreditsForModel,
    useCreditsForModel,
    getCostForModel,
    canAffordModel,
    getGenerationsAvailable,
    getCurrentBalance,
    creditBalance,
  };
}

// Legacy credit requirements for different features
export const CREDIT_COSTS = {
  AI_POST_GENERATION: 2,
  AI_IMAGE_GENERATION: 3,
  AI_CONTENT_OPTIMIZATION: 1,
  AI_HASHTAG_GENERATION: 1,
  AI_CAPTION_ENHANCEMENT: 2,
  BULK_CONTENT_GENERATION: 5,
} as const;

// Helper function to get credit cost for a feature (legacy)
export function getCreditCost(feature: keyof typeof CREDIT_COSTS): number {
  return CREDIT_COSTS[feature] || 1;
}

// Helper function to get model display name
export function getModelDisplayName(modelVersion: ModelVersion): string {
  return {
    'revo-1.0': 'Revo 1.0',
    'revo-1.5': 'Revo 1.5',
    'revo-2.0': 'Revo 2.0',
  }[modelVersion];
}

// Helper function to validate if user can generate with a model
export function validateModelUsage(creditBalance: UserCredits | null, modelVersion: ModelVersion): {
  canUse: boolean;
  cost: number;
  available: number;
  generationsAvailable: number;
} {
  const cost = MODEL_COSTS[modelVersion];
  const available = creditBalance?.remaining_credits || 0;
  
  return {
    canUse: available >= cost,
    cost,
    available,
    generationsAvailable: Math.floor(available / cost),
  };
}