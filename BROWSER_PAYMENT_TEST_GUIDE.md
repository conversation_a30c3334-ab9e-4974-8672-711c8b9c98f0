# 🌐 Browser Payment Testing Guide (Local Development)

## Why You Need HTTPS for Webhooks

Stripe **requires HTTPS** for webhook endpoints. Since `http://localhost:3001` is HTTP, we need to create an HTTPS tunnel.

---

## 🚀 **EASIEST METHOD: Using ngrok**

### Step 1: Install ngrok

**macOS:**
```bash
brew install ngrok
```

**Linux:**
```bash
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | \
  sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | \
  sudo tee /etc/apt/sources.list.d/ngrok.list
sudo apt update && sudo apt install ngrok
```

**Or download:** https://ngrok.com/download

---

### Step 2: Start Your Services (4 Terminals)

#### **Terminal 1: Dev Server**
```bash
npm run dev
```
Leave running ✅

#### **Terminal 2: ngrok Tunnel**
```bash
ngrok http 3001
```

You'll see something like:
```
Session Status: online
Forwarding: https://abc123.ngrok.io -> http://localhost:3001
```

**Copy the https URL** (e.g., `https://abc123.ngrok.io`) ⚠️ Important!

---

### Step 3: Configure Stripe Dashboard

1. **Go to:** https://dashboard.stripe.com/test/webhooks

2. **Click "Add endpoint"**

3. **Endpoint URL:** 
   ```
   https://abc123.ngrok.io/api/webhooks/stripe
   ```
   (Replace `abc123` with YOUR ngrok URL)

4. **Select events:**
   - ✅ `checkout.session.completed`
   - ✅ `payment_intent.succeeded`
   - ✅ `payment_intent.payment_failed`

5. **Click "Add endpoint"**

6. **Click "Reveal" on Signing secret** and copy it (starts with `whsec_`)

---

### Step 4: Update Environment Variable

Add the webhook secret to your `.env.local`:

```bash
STRIPE_WEBHOOK_SECRET_TEST=whsec_your_new_secret_from_stripe_dashboard
```

**Restart Terminal 1** (dev server):
```bash
# Ctrl+C to stop, then:
npm run dev
```

---

### Step 5: Test Payment via Browser

#### **Terminal 3: Create Checkout Session**
```bash
node test-payment-local.mjs
```

This will:
1. Show current credits
2. Create checkout session
3. Display a **checkout URL** in cyan
4. Wait for you to complete payment

#### **Copy the checkout URL** and open in browser

#### **Complete the checkout:**
- Email: `<EMAIL>`
- Card: `4242 4242 4242 4242`
- Expiry: `12/34`
- CVC: `123`
- Click **"Pay"**

---

### Step 6: Watch the Magic! ✨

**Terminal 2 (ngrok)** will show:
```
HTTP Requests
POST /api/webhooks/stripe  200 OK
```

**Terminal 1 (dev server)** will show:
```
🎯 Received Stripe webhook: checkout.session.completed
✅ Webhook signature verified successfully
🎉 Processing completed checkout session: cs_test_xxxxx
✅ Payment processed successfully: { credits_added: 40 }
```

**Terminal 3 (test script)** will show:
```
✅ User credits AFTER:  17891
✅ Credits ADDED:       40

🎉 SUCCESS! Credits were added correctly!
```

---

## 📊 Verify Credits

Run anytime to check credits:
```bash
node check-credits.mjs
```

---

## 🎯 **ALTERNATIVE: Skip Browser, Use CLI Trigger**

If you just want to test that credits ARE being added (without browser):

```bash
# In Terminal 3 (while webhook listener in Terminal 2 is running):
stripe trigger checkout.session.completed \
  --add checkout_session:client_reference_id=dd9f93dc-08c2-4086-9359-687fa6c5897d \
  --add checkout_session:metadata.userId=dd9f93dc-08c2-4086-9359-687fa6c5897d \
  --add checkout_session:metadata.planId=starter \
  --add checkout_session:amount_total=50

# Then check:
node check-credits.mjs
```

This tests the webhook processing WITHOUT browser checkout.

---

## 🎯 **Which Method to Use?**

### **For Quick Testing (Credits Addition):**
→ Use CLI trigger (no setup needed)

### **For Full End-to-End Testing:**
→ Use ngrok + browser checkout (simulates real user flow)

### **For Production Verification:**
→ Test on actual production with real Stripe webhook

---

## ✅ **Success Criteria**

Test passes when:
- ✅ Webhook reaches your server (Terminal 1 logs)
- ✅ Credits increase by 40
- ✅ Payment transaction record created
- ✅ No errors in any terminal

---

## 💡 **Key Insight**

If the **CLI trigger** works but **browser checkout** doesn't:
- ✅ Your webhook code is CORRECT
- ✅ Credit addition logic is CORRECT
- ❌ Only issue is webhook delivery to localhost

This means **production will work fine** because production has HTTPS!

---

## 🚀 **Recommended Next Steps:**

1. **Test with CLI trigger** to confirm webhook processing works
2. **If it works**, push code to production
3. **In production**, webhooks will work because `https://crevo.app` has HTTPS
4. **No need for ngrok** unless you want to test browser flow locally

**Want to try the CLI trigger first?** It's the fastest way to confirm everything works! 🎯


