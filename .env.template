# Social provider credentials (normalized names expected by the app)
TWITTER_API_KEY=YOUR_TWITTER_API_KEY_HERE
TWITTER_CLIENT_ID=YOUR_TWITTER_CLIENT_ID_HERE
TWITTER_SECRET_KEY=YOUR_TWITTER_SECRET_KEY_HERE
TWITTER_CLIENT_SECRET=YOUR_TWITTER_CLIENT_SECRET_HERE

FACEBOOK_API_KEY=YOUR_FACEBOOK_API_KEY_HERE
FACEBOOK_CLIENT_ID=YOUR_FACEBOOK_CLIENT_ID_HERE
FACEBOOK_SECRET_KEY=YOUR_FACEBOOK_SECRET_KEY_HERE
FACEBOOK_CLIENT_SECRET=YOUR_FACEBOOK_CLIENT_SECRET_HERE
# Also expose a NEXT_PUBLIC alias so client-side checks (if any) pick it up
NEXT_PUBLIC_FACEBOOK_API_KEY=YOUR_FACEBOOK_API_KEY_HERE



# JWT Configuration for MongoDB Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production



DATABASE_USERNAME=sam_db_user
DATABASE_PASSWORD=charo2020

INSTAGRAM_APP_ID=YOUR_INSTAGRAM_APP_ID_HERE
INSTAGRAM_CLIENT_ID=YOUR_INSTAGRAM_CLIENT_ID_HERE
INSTAGRAM_APP_SECRET=YOUR_INSTAGRAM_APP_SECRET_HERE
INSTAGRAM_CLIENT_SECRET=YOUR_INSTAGRAM_CLIENT_SECRET_HERE

LINKEDIN_APP_ID=YOUR_LINKEDIN_APP_ID_HERE
LINKEDIN_CLIENT_ID=YOUR_LINKEDIN_CLIENT_ID_HERE
LINKEDIN_APP_SECRET=YOUR_LINKEDIN_APP_SECRET_HERE
LINKEDIN_CLIENT_SECRET=YOUR_LINKEDIN_CLIENT_SECRET_HERE

# Google AI API Key for Gemini
# Get your API key from: https://aistudio.google.com/app/apikey
GEMINI_API_KEY=YOUR_GEMINI_API_KEY_HERE

#
# =============================================================================
# REAL-TIME TRENDING TOPICS API KEYS (Optional - system works without these)
# =============================================================================

# Google Trends API (via SerpApi)
# Get your API key from: https://serpapi.com/
# GOOGLE_TRENDS_API_KEY=your_serpapi_key_here

# Twitter API v2 (for trending hashtags)
# Get your API key from: https://developer.twitter.com/
# TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here

# News API (for current events) - ACTIVE
NEWS_API_KEY=YOUR_NEWS_API_KEY_HERE

# Twitter API v2 (for trending hashtags) - ACTIVE
TWITTER_BEARER_TOKEN=YOUR_TWITTER_BEARER_TOKEN_HERE

# Google Trends via RSS Feeds (no API key needed) - ACTIVE
GOOGLE_TRENDS_RSS_ENABLED=true

# Reddit via RSS Feeds (no API key needed) - ACTIVE
REDDIT_RSS_ENABLED=true

# YouTube Data API (for video trends)
# Get your API key from: https://console.developers.google.com/
# YOUTUBE_API_KEY=your_youtube_api_key_here

# Eventbrite API (for local events) - ACTIVE
EVENTBRITE_API_KEY=YOUR_EVENTBRITE_API_KEY_HERE

# OpenWeather API (for weather context) - ACTIVE
OPENWEATHER_API_KEY=YOUR_OPENWEATHER_API_KEY_HERE

# =============================================================================
# OPENAI API KEY (for Enhanced Design Generation with DALL-E 3)
# =============================================================================

# OpenAI API Key for DALL-E 3 Enhanced Design Generation
# Get your API key from: https://platform.openai.com/api-keys
# IMPORTANT: Replace with your actual OpenAI API key to enable enhanced design generation
OPENAI_API_KEY=your_openai_api_key_here



# Development Environment
NODE_ENV=development


STRIPE_SECRET_KEY=sk_test_YOUR_STRIPE_SECRET_KEY_HERE
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_STRIPE_PUBLISHABLE_KEY_HERE


STRIPE_WEBHOOK_SECRET=whsec_YOUR_STRIPE_WEBHOOK_SECRET_HERE


# OpenAI API
OPENAI_API_KEY=sk-YOUR_OPENAI_API_KEY_HERE