#!/bin/bash

# Check recent Stripe events and webhook deliveries

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🔍 STRIPE WEBHOOK DIAGNOSTICS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "Please answer these questions by checking Stripe Dashboard:"
echo ""
echo "1️⃣  Go to: https://dashboard.stripe.com/test/events"
echo ""
echo "2️⃣  Do you see recent checkout.session.completed events?"
echo "    (From your $0.50 or $24.99 payments)"
echo ""
echo "3️⃣  Click on the MOST RECENT checkout.session.completed event"
echo ""
echo "4️⃣  Scroll to 'Deliveries to webhook endpoints' section"
echo ""
echo "5️⃣  What does it say?"
echo "    a) 'No webhook endpoints were configured when this event occurred'"
echo "    b) Shows delivery to: https://0655bf5a38ca.ngrok-free.app/api/webhooks/stripe"
echo "    c) Shows delivery to a different URL"
echo "    d) Shows 'Failed' delivery"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "Also check:"
echo ""
echo "6️⃣  Go to: https://dashboard.stripe.com/test/webhooks"
echo ""
echo "7️⃣  How many webhook endpoints do you see?"
echo ""
echo "8️⃣  Which ones show 'Enabled' status?"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"

