# 🧪 Local Webhook Testing Guide

## Complete guide to test Stripe webhooks locally and verify credit addition

---

## 📋 Prerequisites

### 1. Install Stripe CLI

**macOS:**
```bash
brew install stripe/stripe-cli/stripe
```

**Linux:**
```bash
# Download and install
wget https://github.com/stripe/stripe-cli/releases/latest/download/stripe_linux_x86_64.tar.gz
tar -xvf stripe_linux_x86_64.tar.gz
sudo mv stripe /usr/local/bin/
```

**Windows:**
Download from: https://github.com/stripe/stripe-cli/releases

### 2. Setup Environment Variables

Make sure your `.env.local` file has:

```bash
# Stripe Test Keys
STRIPE_SECRET_KEY_TEST=sk_test_YOUR_TEST_KEY
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST=pk_test_YOUR_TEST_KEY
STRIPE_WEBHOOK_SECRET_TEST=whsec_YOUR_TEST_SECRET  # Will be generated by Stripe CLI

# Or fallback keys
STRIPE_SECRET_KEY=sk_test_YOUR_TEST_KEY
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_YOUR_TEST_KEY
STRIPE_WEBHOOK_SECRET=whsec_YOUR_TEST_SECRET

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Test User (get from your database)
TEST_USER_ID=your-test-user-uuid

# JWT
JWT_SECRET=your-jwt-secret
```

---

## 🚀 Testing Steps

### Step 1: Start Development Server

In **Terminal 1**:
```bash
npm run dev
```

Wait for server to start on `http://localhost:3001`

---

### Step 2: Start Stripe Webhook Listener

In **Terminal 2**:
```bash
./test-webhook-local-complete.sh
```

This will:
- Check if Stripe CLI is installed
- Login to Stripe (if needed)
- Start forwarding webhooks to your local server
- Give you a webhook signing secret

**Important:** When the listener starts, you'll see:
```
> Ready! Your webhook signing secret is whsec_xxxxxxxxxxxxx
```

Copy this secret and add it to your `.env.local`:
```bash
STRIPE_WEBHOOK_SECRET_TEST=whsec_xxxxxxxxxxxxx
```

Keep this terminal running!

---

### Step 3: Run Payment Test

In **Terminal 3**:
```bash
node test-payment-local.mjs
```

This will:
1. ✅ Check user credits BEFORE payment
2. ✅ Create a test checkout session
3. ✅ Simulate payment completion
4. ✅ Wait for webhook to process
5. ✅ Check user credits AFTER payment
6. ✅ Verify credits were added (+40)
7. ✅ Check payment transaction record

---

## 📊 Expected Output

### Success Case ✅

```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🧪 LOCAL PAYMENT TEST WITH CREDIT VERIFICATION
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📋 Step 1: Check user credits BEFORE payment
✅ User credits BEFORE: 10

📋 Step 2: Create test checkout session
✅ Checkout session created: cs_test_xxxxx

📋 Step 3: Simulate payment completion
✅ Payment intent created: pi_xxxxx
   Status: succeeded

📋 Step 4: Wait for webhook to process...
⏳ Waiting 5 seconds for webhook...

📋 Step 5: Check user credits AFTER payment
✅ User credits AFTER:  50
✅ Credits ADDED:       40

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📊 RESULT
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

🎉 SUCCESS! Credits were added correctly!
   Expected: 40 credits
   Actual:   40 credits

✅ Payment flow is working correctly!

📋 Step 6: Check payment transaction record
✅ Payment record found:
   Transaction ID: 123
   Plan ID: starter
   Amount: 0.5
   Credits Added: 40
   Status: completed
```

### Failure Case ❌

If no credits are added:
```
⚠️  WARNING: No credits were added!

Possible issues:
  - Webhook listener not running
  - Webhook processing failed
  - Database RPC function error

Check:
  1. Webhook listener terminal for errors
  2. Dev server console for webhook logs
  3. Supabase logs for RPC errors
```

---

## 🔍 What to Watch For

### Terminal 2 (Webhook Listener)

You should see:
```
2025-10-18 09:30:15   --> checkout.session.completed [evt_xxxxx]
2025-10-18 09:30:15  <--  [200] POST http://localhost:3001/api/webhooks/stripe [evt_xxxxx]
```

**200** = Success ✅  
**400** = Invalid signature ❌  
**500** = Server error ❌

### Terminal 1 (Dev Server)

Look for logs like:
```
🎯 Received Stripe webhook: checkout.session.completed
✅ Webhook signature verified successfully
🎉 Processing completed checkout session: cs_test_xxxxx
✅ Found plan: { planId: 'starter', credits: 40, name: 'Starter Agent' }
✅ Payment processed successfully: { credits_added: 40 }
```

---

## 🐛 Troubleshooting

### Issue: "Stripe CLI not found"

**Solution:**
- Install Stripe CLI (see Prerequisites above)
- Run: `stripe version` to verify

### Issue: "Webhook signature verification failed"

**Solution:**
1. Make sure webhook listener is running
2. Copy the `whsec_` secret from listener output
3. Add to `.env.local` as `STRIPE_WEBHOOK_SECRET_TEST`
4. Restart dev server
5. Try again

### Issue: "User not found"

**Solution:**
1. Check your Supabase `users` table
2. Find a valid user UUID
3. Add to `.env.local`:
   ```bash
   TEST_USER_ID=your-actual-user-uuid
   ```

### Issue: "No credits were added"

**Possible causes:**
1. ❌ Webhook listener not running
2. ❌ Webhook signature failed
3. ❌ Database RPC function missing/broken
4. ❌ User ID mismatch

**Debug steps:**
1. Check Terminal 2 for webhook delivery
2. Check Terminal 1 for processing logs
3. Check Supabase logs for RPC errors
4. Verify `process_payment_with_idempotency` function exists

### Issue: "RPC function not found"

**Solution:**
Run the database migration:
```sql
-- In Supabase SQL Editor
-- Paste the RPC function from your migration files
```

---

## ✅ Success Criteria

Test is successful when:
- ✅ Webhook listener receives events (200 OK)
- ✅ Dev server processes webhook successfully
- ✅ User credits increase by 40
- ✅ Payment transaction record is created
- ✅ No errors in any terminal

---

## 🚀 After Local Testing Succeeds

Once local testing works:

1. **Commit and push** your code
2. **Deploy to production** (Vercel)
3. **Verify environment variables** in Vercel Dashboard
4. **Test on production** with real payment
5. **Resend old failed webhooks** from Stripe Dashboard

---

## 💡 Pro Tips

### Quick Test Loop

```bash
# Terminal 1
npm run dev

# Terminal 2  
./test-webhook-local-complete.sh

# Terminal 3 - Run this multiple times to test
node test-payment-local.mjs
```

### Check Credits Quickly

```bash
# In Terminal 3
node -e "
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });
const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);
(async () => {
  const { data } = await supabase
    .from('users')
    .select('credits')
    .eq('user_id', process.env.TEST_USER_ID)
    .single();
  console.log('Current credits:', data?.credits);
})();
"
```

### Reset Test User Credits

```bash
# In Supabase SQL Editor
UPDATE users 
SET credits = 0 
WHERE user_id = 'your-test-user-id';
```

---

## 📞 Need Help?

If tests fail:
1. Check all three terminals for errors
2. Verify `.env.local` has all required variables
3. Confirm Supabase functions are deployed
4. Check Stripe test mode is active
5. Review webhook logs in Stripe Dashboard

**Remember:** If it works locally with the same code and webhook secret, it will work in production!


