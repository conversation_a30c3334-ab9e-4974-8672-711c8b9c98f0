/**
 * Brand Voice Enforcer
 * Ensures content matches appropriate brand voice and prevents aggressive/scam-like styling
 */

export interface BrandVoiceGuidelines {
  businessType: string;
  location: string;
  allowedTones: string[];
  forbiddenTones: string[];
  preferredLanguage: string[];
  forbiddenLanguage: string[];
  visualStyleGuidelines: string[];
  forbiddenVisualStyles: string[];
}

export class BrandVoiceEnforcer {
  
  /**
   * Get brand voice guidelines based on business type and location
   */
  static getBrandVoiceGuidelines(businessType: string, location: string): BrandVoiceGuidelines {
    const businessLower = businessType.toLowerCase();
    const locationLower = location.toLowerCase();
    
    // Financial services require special trust-focused guidelines
    if (businessLower.includes('financial') || businessLower.includes('fintech') || 
        businessLower.includes('bank') || businessLower.includes('payment')) {
      return this.getFinancialServiceGuidelines(location);
    }
    
    // Healthcare requires professional, caring guidelines
    if (businessLower.includes('health') || businessLower.includes('medical') || 
        businessLower.includes('clinic') || businessLower.includes('hospital')) {
      return this.getHealthcareGuidelines(location);
    }
    
    // Education requires trustworthy, empowering guidelines
    if (businessLower.includes('education') || businessLower.includes('school') || 
        businessLower.includes('training') || businessLower.includes('learning')) {
      return this.getEducationGuidelines(location);
    }
    
    // Default business guidelines
    return this.getGeneralBusinessGuidelines(businessType, location);
  }
  
  /**
   * Financial services brand voice guidelines - CRITICAL for preventing crypto-scam appearance
   */
  private static getFinancialServiceGuidelines(location: string): BrandVoiceGuidelines {
    return {
      businessType: 'financial',
      location,
      allowedTones: [
        'trustworthy', 'reliable', 'secure', 'professional', 'empowering',
        'community-focused', 'accessible', 'transparent', 'supportive'
      ],
      forbiddenTones: [
        'aggressive', 'hype', 'urgent', 'scammy', 'crypto-like', 'get-rich-quick',
        'revolutionary', 'breakthrough', 'cutting-edge', 'disruptive'
      ],
      preferredLanguage: [
        'secure mobile payments', 'send money instantly', 'grow your business',
        'trusted financial services', 'safe digital banking', 'reliable payments',
        'flexible financing', 'community banking', 'accessible financial solutions',
        'transparent fees', 'local financial partner', 'secure transactions'
      ],
      forbiddenLanguage: [
        'breakthrough technology', 'revolutionary fintech', 'disrupt banking',
        'crypto revolution', 'financial breakthrough', 'next-gen payments',
        'cutting-edge fintech', 'game-changing technology', 'paradigm shift',
        'unlock wealth', 'maximize profits', 'explosive growth'
      ],
      visualStyleGuidelines: [
        'clean and professional', 'trustworthy colors (blues, greens)',
        'community-focused imagery', 'real people in authentic settings',
        'warm and approachable', 'local cultural elements', 'family-oriented'
      ],
      forbiddenVisualStyles: [
        'neon cyber style', 'aggressive neon colors', 'crypto-scam aesthetics',
        'flashy graphics', 'overwhelming effects', 'sci-fi styling',
        'dark cyber themes', 'matrix-like effects', 'aggressive typography'
      ]
    };
  }
  
  /**
   * Healthcare brand voice guidelines
   */
  private static getHealthcareGuidelines(location: string): BrandVoiceGuidelines {
    return {
      businessType: 'healthcare',
      location,
      allowedTones: [
        'caring', 'professional', 'trustworthy', 'compassionate', 'reliable',
        'knowledgeable', 'supportive', 'accessible', 'community-focused'
      ],
      forbiddenTones: [
        'aggressive', 'pushy', 'fear-mongering', 'overly technical', 'cold'
      ],
      preferredLanguage: [
        'quality healthcare', 'compassionate care', 'trusted medical services',
        'community health', 'professional treatment', 'accessible healthcare'
      ],
      forbiddenLanguage: [
        'revolutionary treatment', 'breakthrough medicine', 'cutting-edge procedures'
      ],
      visualStyleGuidelines: [
        'clean and calming', 'professional medical imagery', 'warm colors',
        'real healthcare professionals', 'community-focused'
      ],
      forbiddenVisualStyles: [
        'aggressive styling', 'overwhelming graphics', 'cold clinical look'
      ]
    };
  }
  
  /**
   * Education brand voice guidelines
   */
  private static getEducationGuidelines(location: string): BrandVoiceGuidelines {
    return {
      businessType: 'education',
      location,
      allowedTones: [
        'empowering', 'supportive', 'encouraging', 'professional', 'accessible',
        'community-focused', 'inspiring', 'trustworthy', 'knowledgeable'
      ],
      forbiddenTones: [
        'condescending', 'overly academic', 'intimidating', 'aggressive'
      ],
      preferredLanguage: [
        'quality education', 'empowering learning', 'supportive environment',
        'accessible education', 'community learning', 'skill development'
      ],
      forbiddenLanguage: [
        'revolutionary education', 'breakthrough learning', 'disruptive teaching'
      ],
      visualStyleGuidelines: [
        'inspiring and uplifting', 'diverse learners', 'bright and welcoming',
        'real educational settings', 'community-focused imagery'
      ],
      forbiddenVisualStyles: [
        'intimidating academic imagery', 'overly formal styling', 'cold institutional look'
      ]
    };
  }
  
  /**
   * General business brand voice guidelines
   */
  private static getGeneralBusinessGuidelines(businessType: string, location: string): BrandVoiceGuidelines {
    return {
      businessType,
      location,
      allowedTones: [
        'professional', 'friendly', 'reliable', 'trustworthy', 'community-focused',
        'accessible', 'quality-focused', 'customer-oriented'
      ],
      forbiddenTones: [
        'aggressive', 'pushy', 'overly salesy', 'scammy', 'fake'
      ],
      preferredLanguage: [
        'quality service', 'professional approach', 'reliable solutions',
        'community-focused', 'trusted local business', 'customer satisfaction'
      ],
      forbiddenLanguage: [
        'revolutionary breakthrough', 'cutting-edge solutions', 'disruptive innovation'
      ],
      visualStyleGuidelines: [
        'professional and approachable', 'real people and settings',
        'community-focused imagery', 'authentic representation'
      ],
      forbiddenVisualStyles: [
        'overly flashy', 'aggressive styling', 'fake-looking imagery'
      ]
    };
  }
  
  /**
   * Validate content against brand voice guidelines
   */
  static validateBrandVoice(content: {
    headline?: string;
    subheadline?: string;
    caption?: string;
    callToAction?: string;
  }, businessType: string, location: string): {
    isValid: boolean;
    issues: string[];
    fixes: string[];
    validatedContent: any;
  } {
    const guidelines = this.getBrandVoiceGuidelines(businessType, location);
    const issues: string[] = [];
    const fixes: string[] = [];
    let validatedContent = { ...content };
    
    // Check each content element against guidelines
    Object.entries(content).forEach(([key, value]) => {
      if (typeof value === 'string') {
        const validation = this.validateTextAgainstGuidelines(value, guidelines, key);
        if (validation.issues.length > 0) {
          issues.push(...validation.issues);
          fixes.push(...validation.fixes);
          validatedContent[key] = validation.validatedText;
        }
      }
    });
    
    return {
      isValid: issues.length === 0,
      issues,
      fixes,
      validatedContent
    };
  }
  
  /**
   * Validate text against brand voice guidelines
   */
  private static validateTextAgainstGuidelines(text: string, guidelines: BrandVoiceGuidelines, contentType: string): {
    issues: string[];
    fixes: string[];
    validatedText: string;
  } {
    const issues: string[] = [];
    const fixes: string[] = [];
    let validatedText = text;
    
    // Check for forbidden language
    for (const forbiddenPhrase of guidelines.forbiddenLanguage) {
      if (validatedText.toLowerCase().includes(forbiddenPhrase.toLowerCase())) {
        issues.push(`❌ Forbidden phrase in ${contentType}: "${forbiddenPhrase}"`);
        
        // Replace with appropriate alternative
        const replacement = this.getAppropriateReplacement(forbiddenPhrase, guidelines.businessType);
        validatedText = validatedText.replace(new RegExp(forbiddenPhrase, 'gi'), replacement);
        fixes.push(`Replaced "${forbiddenPhrase}" with "${replacement}"`);
      }
    }
    
    return { issues, fixes, validatedText };
  }
  
  /**
   * Get appropriate replacement for forbidden phrases
   */
  private static getAppropriateReplacement(forbiddenPhrase: string, businessType: string): string {
    const replacements: Record<string, Record<string, string>> = {
      financial: {
        'breakthrough technology': 'reliable service',
        'revolutionary fintech': 'trusted financial services',
        'disrupt banking': 'improve banking',
        'financial breakthrough': 'secure payments',
        'cutting-edge fintech': 'modern banking',
        'game-changing technology': 'reliable technology'
      },
      general: {
        'revolutionary breakthrough': 'quality service',
        'cutting-edge solutions': 'reliable solutions',
        'disruptive innovation': 'helpful innovation'
      }
    };
    
    const businessReplacements = replacements[businessType] || replacements.general;
    return businessReplacements[forbiddenPhrase.toLowerCase()] || 'quality service';
  }
  
  /**
   * Get visual style instructions that prevent aggressive/scam-like appearance
   */
  static getVisualStyleInstructions(businessType: string, location: string): string {
    const guidelines = this.getBrandVoiceGuidelines(businessType, location);
    
    const allowedStyles = guidelines.visualStyleGuidelines.join(', ');
    const forbiddenStyles = guidelines.forbiddenVisualStyles.join(', ');
    
    return `
🎨 BRAND VOICE VISUAL GUIDELINES:
✅ REQUIRED VISUAL STYLE: ${allowedStyles}
❌ FORBIDDEN VISUAL STYLES: ${forbiddenStyles}

🌟 TONE REQUIREMENTS:
- Use ${guidelines.allowedTones.join(', ')} tone
- NEVER use ${guidelines.forbiddenTones.join(', ')} tone

🗣️ LANGUAGE REQUIREMENTS:
- Preferred language: ${guidelines.preferredLanguage.slice(0, 3).join(', ')}
- FORBIDDEN language: ${guidelines.forbiddenLanguage.slice(0, 3).join(', ')}
`;
  }
}
