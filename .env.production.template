# PRODUCTION ENVIRONMENT VARIABLES
# Copy this to .env.local for production deployment

# =============================================================================
# SUPABASE CONFIGURATION (PRODUCTION)
# =============================================================================
# Get these from your Supabase project dashboard
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# =============================================================================
# STRIPE CONFIGURATION (PRODUCTION)
# =============================================================================
# CRITICAL: Use LIVE keys for production (start with pk_live_ and sk_live_)
STRIPE_SECRET_KEY=sk_live_51...
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_51...
STRIPE_WEBHOOK_SECRET=whsec_...

# =============================================================================
# GEMINI API KEYS (PRODUCTION)
# =============================================================================
# Use your production Gemini API keys
GEMINI_API_KEY=AIzaSyDwxxaDbNw8yLJ0gzvFrYwO0lL90J-eCuQ
GEMINI_API_KEY_REVO_1_0=AIzaSyDwxxaDbNw8yLJ0gzvFrYwO0lL90J-eCuQ
GEMINI_API_KEY_REVO_1_5=AIzaSyDSPjcvfq5uRV_mZrlOKi2dWdO1SxGzgkM
GEMINI_API_KEY_REVO_2_0=AIzaSyBOmZ1eIej4Vzab...

# =============================================================================
# JWT CONFIGURATION (PRODUCTION)
# =============================================================================
# CRITICAL: Generate strong, unique secrets for production
JWT_SECRET=your-super-secure-jwt-secret-256-bits-minimum-length-for-production-security
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-256-bits-minimum-length-for-production

# =============================================================================
# ADMIN CONFIGURATION (PRODUCTION)
# =============================================================================
# CRITICAL: Generate a strong admin secret for migration endpoints
ADMIN_SECRET=your-ultra-secure-admin-secret-for-migration-endpoints-production-2024

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# =============================================================================
# OPTIONAL: SOCIAL MEDIA OAUTH (if using)
# =============================================================================
TWITTER_CLIENT_ID=your-twitter-client-id
TWITTER_CLIENT_SECRET=your-twitter-client-secret
TWITTER_REDIRECT_URI=https://your-domain.com/api/twitter/auth/callback

FACEBOOK_CLIENT_ID=your-facebook-app-id
FACEBOOK_CLIENT_SECRET=your-facebook-app-secret
FACEBOOK_REDIRECT_URI=https://your-domain.com/api/auth/facebook/callback

LINKEDIN_CLIENT_ID=your-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret
LINKEDIN_REDIRECT_URI=https://your-domain.com/api/auth/linkedin/callback

# OAuth encryption key (32+ characters)
OAUTH_ENCRYPTION_KEY=your-oauth-encryption-key-32-characters-minimum-for-production-security

# =============================================================================
# WEBHOOK CONFIGURATION
# =============================================================================
# Base URL for webhooks (your production domain)
NEXT_PUBLIC_WEBHOOK_BASE_URL=https://your-domain.com
WEBHOOK_SECRET_KEY=your-webhook-security-token-2024
