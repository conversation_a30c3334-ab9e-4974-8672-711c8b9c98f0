<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1" />
    <title>Payment Successful</title>
    <style>
      :root{--bg:#f8fafc;--card:#fff;--muted:#64748b;--accent:#059669}
      body{font-family:Inter,ui-sans-serif,system-ui,Segoe UI,Roboto,Helvetica,Arial,sans-serif;background:var(--bg);color:#0f172a;display:flex;align-items:center;justify-content:center;height:100vh;margin:0}
      .card{max-width:720px;padding:28px;border-radius:12px;background:var(--card);box-shadow:0 6px 20px rgba(2,6,23,0.06);text-align:center}
      h1{margin:0 0 8px;font-size:22px;color:var(--accent)}
      p{margin:0 0 16px;color:var(--muted)}
      .meta{margin-top:12px;font-size:13px;color:#475569;word-break:break-all}
      .actions{margin-top:18px}
      .btn{display:inline-block;padding:10px 16px;border-radius:8px;background:var(--accent);color:#fff;text-decoration:none;font-weight:600;margin:6px}
      .btn-outline{background:transparent;border:1px solid #e2e8f0;color:#0f172a}
      .toast{background:#fef3c7;border-left:4px solid #f59e0b;padding:10px 12px;margin-bottom:12px;border-radius:6px;color:#78350f}
      .hidden{display:none}
    </style>
  </head>
  <body>
    <div class="card" role="main">
      <div id="toast" class="toast hidden">Payment processed — details below.</div>
      <div style="display:flex;align-items:center;justify-content:center;margin-bottom:12px">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" aria-hidden="true"><path d="M9 12l2 2 4-4" stroke="#059669" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><circle cx="12" cy="12" r="9" stroke="#059669" stroke-width="1.5"/></svg>
      </div>
      <h1>Payment Successful</h1>
      <p>Your payment was processed. No further navigation is required — this page shows your purchase details.</p>

      <div id="summary" class="meta">Session: <span id="session">—</span></div>
      <div id="amount" class="meta hidden">Amount: <span id="amt">—</span></div>
      <div class="actions">
        <a id="home" class="btn-outline btn" href="/">Back to Home</a>
        <button id="dismiss" class="btn btn-outline">Dismiss</button>
      </div>
    </div>

    <script>
      (function(){
        function qs(key){
          const params = new URLSearchParams(location.search);
          return params.get(key);
        }

        const session = qs('session_id') || qs('session') || '';
        const sessionEl = document.getElementById('session');
        const amtEl = document.getElementById('amount');
        const amtValEl = document.getElementById('amt');
        const toast = document.getElementById('toast');

        if(session){
          sessionEl.textContent = session;
          // show toast briefly to confirm
          toast.classList.remove('hidden');
          setTimeout(()=>{ toast.classList.add('hidden'); }, 3500);

          // Try to fetch session details (best-effort) but don't require it
          fetch('/api/payments/session-details', { method: 'POST', headers: {'Content-Type':'application/json'}, body: JSON.stringify({ sessionId: session }) })
            .then(r=>r.json()).then(j=>{
              if(j && j.ok){
                const amount = (j.amountCents||0)/100;
                amtValEl.textContent = (j.currency||'USD').toUpperCase() + ' ' + amount.toFixed(2);
                amtEl.classList.remove('hidden');
              }
            }).catch(()=>{
              // ignore errors — we still show session id
            });
        } else {
          sessionEl.textContent = '—';
        }

        document.getElementById('dismiss').addEventListener('click', function(){
          // simply hide the card (user asked no nav to dashboard)
          document.body.innerHTML = '<div style="font-family:Inter,system-ui;display:flex;height:100vh;align-items:center;justify-content:center">Thanks — you can close this tab.</div>';
        });
      })();
    </script>
  </body>
</html>
