<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clear All Cache - Debug Mode</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        .button:hover {
            background: #0056b3;
        }
        .button.danger {
            background: #dc3545;
        }
        .button.danger:hover {
            background: #c82333;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            font-family: monospace;
            margin: 10px 0;
        }
        .step {
            background: #fff3cd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #ffc107;
        }
        .test-result {
            background: #d4edda;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #28a745;
        }
        .debug-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Clear All Cache - Debug Mode</h1>
        <p>This tool will clear ALL cached data and force a complete fresh load from the database.</p>
        
        <div class="test-result">
            <h3>✅ Database Confirmed:</h3>
            <p>The database has the correct updated data:</p>
            <div class="code">
                <strong>Website URL:</strong> https://enhanced-edit-fix-test.com/<br>
                <strong>Contact Website:</strong> https://enhanced-edit-fix-test.com/
            </div>
        </div>
        
        <div class="debug-info">
            <h3>🐛 Debug Information:</h3>
            <p><strong>Current Issue:</strong> Frontend showing old cached URL despite database having correct data</p>
            <p><strong>Expected:</strong> Website URL should show <code>https://enhanced-edit-fix-test.com/</code></p>
            <p><strong>Actual:</strong> Website URL showing <code>https://blackpanthertkn.com/</code></p>
        </div>
        
        <h3>🛠️ Complete Cache Clearing Solution:</h3>
        
        <button class="button" onclick="clearAllCaches()">1. Clear All Caches</button>
        <button class="button" onclick="clearLocalStorage()">2. Clear localStorage</button>
        <button class="button" onclick="clearSessionStorage()">3. Clear sessionStorage</button>
        <button class="button danger" onclick="nuclearOption()">4. Nuclear Option - Clear Everything</button>
        <button class="button" onclick="openBrandProfile()">5. Open Brand Profile</button>
        <button class="button" onclick="checkCurrentData()">6. Check Current Data</button>
        
        <div id="status"></div>
        
        <div class="step">
            <h3>📝 Step-by-Step Debug Process:</h3>
            <ol>
                <li><strong>Clear All Caches:</strong> Click "Nuclear Option - Clear Everything"</li>
                <li><strong>Open Brand Profile:</strong> Click "Open Brand Profile" button</li>
                <li><strong>Check Console:</strong> Open browser developer tools (F12)</li>
                <li><strong>Look for Logs:</strong> Check console for these logs:
                    <div class="code">
                        🔄 Edit mode: Refreshing brand data from database...<br>
                        ✅ Using refreshed brand data: Black Panther TKN<br>
                        ✅ Website URL: https://enhanced-edit-fix-test.com/<br>
                        🔄 Setting brand profile state...<br>
                        🔄 Forcing brand profile re-render...<br>
                        🔄 WebsiteAnalysisStep: brandProfile.websiteUrl changed: https://enhanced-edit-fix-test.com/<br>
                        ✅ WebsiteAnalysisStep: Updating websiteUrl from [old] to [new]
                    </div>
                </li>
                <li><strong>Check Website URL Field:</strong> Should show <code>https://enhanced-edit-fix-test.com/</code></li>
            </ol>
        </div>
        
        <div class="test-result">
            <h3>✅ Expected Result After Clearing Cache:</h3>
            <div class="code">
                Black Panther TKN<br>
                Tech Startup / Cryptocurrency Token Project<br>
                Active<br>
                California, USA<br>
                <strong>https://enhanced-edit-fix-test.com/</strong> ← Should show this instead of blackpanthertkn.com
            </div>
        </div>
        
        <div class="info">
            <h3>🔍 If Still Not Working:</h3>
            <p>If the website URL still shows the old value after clearing cache, the issue might be:</p>
            <ol>
                <li><strong>Component State Issue:</strong> The WebsiteAnalysisStep component is not updating its state</li>
                <li><strong>Props Not Updating:</strong> The brandProfile prop is not being passed correctly</li>
                <li><strong>useEffect Not Triggering:</strong> The useEffect in WebsiteAnalysisStep is not running</li>
                <li><strong>State Initialization:</strong> The component is initializing with old data</li>
            </ol>
        </div>
        
        <div class="debug-info">
            <h3>🧪 Debug Checklist:</h3>
            <ul>
                <li>✅ Database has correct data</li>
                <li>✅ Cache cleared completely</li>
                <li>❓ Console shows refresh logs</li>
                <li>❓ WebsiteAnalysisStep receives updated props</li>
                <li>❓ useEffect triggers with new data</li>
                <li>❓ setWebsiteUrl is called</li>
                <li>❓ Input field updates with new value</li>
            </ul>
        </div>
    </div>

    <script>
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.innerHTML = `<div class="${isError ? 'error' : 'success'}">${message}</div>`;
        }

        function clearAllCaches() {
            try {
                // Clear localStorage
                const localStorageKeys = [
                    'currentBrandData',
                    'completeBrandProfile',
                    'selectedBrandId',
                    'currentBrandId',
                    'currentBrandName',
                    'brandProfiles',
                    'currentBrand',
                    'brandColors',
                    'BRAND_DRAFT_d2f65044-9b2b-47c5-91ae-9be038be20c3'
                ];
                
                let clearedCount = 0;
                localStorageKeys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        clearedCount++;
                    }
                });
                
                // Clear sessionStorage
                sessionStorage.clear();
                
                // Clear service worker cache if available
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                
                showStatus(`✅ Cleared ${clearedCount} localStorage items, sessionStorage, and caches!`);
            } catch (error) {
                showStatus(`❌ Error clearing caches: ${error.message}`, true);
            }
        }

        function clearLocalStorage() {
            try {
                localStorage.clear();
                showStatus('✅ Cleared all localStorage successfully!');
            } catch (error) {
                showStatus(`❌ Error clearing localStorage: ${error.message}`, true);
            }
        }

        function clearSessionStorage() {
            try {
                sessionStorage.clear();
                showStatus('✅ Cleared sessionStorage successfully!');
            } catch (error) {
                showStatus(`❌ Error clearing sessionStorage: ${error.message}`, true);
            }
        }

        function nuclearOption() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                if ('caches' in window) {
                    caches.keys().then(names => {
                        names.forEach(name => {
                            caches.delete(name);
                        });
                    });
                }
                showStatus('✅ Nuclear option: Cleared ALL storage and caches!');
            } catch (error) {
                showStatus(`❌ Error with nuclear option: ${error.message}`, true);
            }
        }

        function openBrandProfile() {
            try {
                const url = 'http://localhost:3001/brand-profile?mode=edit&id=d2f65044-9b2b-47c5-91ae-9be038be20c3';
                window.open(url, '_blank');
                showStatus('✅ Opening Black Panther TKN brand profile page in new tab...');
            } catch (error) {
                showStatus(`❌ Error opening page: ${error.message}`, true);
            }
        }

        function checkCurrentData() {
            try {
                const currentBrandData = localStorage.getItem('currentBrandData');
                const completeBrandProfile = localStorage.getItem('completeBrandProfile');
                const selectedBrandId = localStorage.getItem('selectedBrandId');
                
                showStatus(`📊 Current Data Check:<br>
                    currentBrandData: ${currentBrandData ? 'EXISTS' : 'NOT FOUND'}<br>
                    completeBrandProfile: ${completeBrandProfile ? 'EXISTS' : 'NOT FOUND'}<br>
                    selectedBrandId: ${selectedBrandId || 'NOT FOUND'}<br>
                    <br>
                    <strong>If any of these exist, they might contain old cached data!</strong>`);
            } catch (error) {
                showStatus(`❌ Error checking data: ${error.message}`, true);
            }
        }

        // Show initial status
        showStatus('Ready to clear all caches and debug the brand update issue. Click the buttons above to proceed.');
    </script>
</body>
</html>




